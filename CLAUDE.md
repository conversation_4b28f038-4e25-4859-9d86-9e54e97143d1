# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Apex Solver is a Rust-based nonlinear least squares optimization library focused on computer vision applications like bundle adjustment, SLAM, and pose graph optimization. It provides manifold operations for Lie groups (SE2, SE3, SO2, SO3) and supports multiple optimization algorithms with configurable linear algebra backends.

## Core Architecture

The codebase is organized into five main modules:

- **`core/`**: Optimization problem definitions, factors, correctors, and residual blocks
- **`optimizer/`**: Three optimization algorithms (<PERSON><PERSON><PERSON>, Gauss-Newton, Dog Leg) with unified configuration system
- **`linalg/`**: Linear algebra backends (Sparse Cho<PERSON>ky, Sparse QR) built on the `faer` library
- **`manifold/`**: Lie group implementations (SE2, SE3, SO2, SO3) with analytic Jacobians
- **`io/`**: File format support for G2O, TORO, and TUM trajectory formats

Key design patterns:
- **Configuration-based solver creation**: Use `OptimizerConfig` with `SolverFactory::create_solver()`
- **Unified solver interface**: All solvers implement the `Solver` trait with consistent `SolverResult` output
- **Type-safe manifold operations**: Lie groups provide `plus()`, `minus()`, and Jacobian methods
- **Flexible linear algebra**: Switch between Cholesky and QR backends via `LinearSolverType`

## Common Development Commands

```bash
# Build the project
cargo build

# Run all tests
cargo test

# Run linting
cargo clippy

# Format code
cargo fmt

# Run specific example
cargo run --example load_graph_file
cargo run --example visualize_graph_file

# Run benchmarks
cargo bench
```

## Key Dependencies

- **`faer`**: Core linear algebra operations and sparse matrix support
- **`nalgebra`**: Additional linear algebra utilities
- **`rerun`**: 3D visualization support in examples
- **`thiserror`**: Error handling
- **`rayon`**: Parallelization

## Testing Strategy

- Unit tests are embedded within each module using `#[cfg(test)]`
- Integration tests are in `src/linalg/integration_tests.rs`
- Example programs serve as integration tests for file I/O and visualization
- Use `cargo test` to run all tests, including doctests

## Solver Configuration

Always create solvers through the configuration system:

```rust
use apex_solver::{OptimizerConfig, OptimizerType, LinearSolverType, SolverFactory};

let config = OptimizerConfig::new()
    .with_optimizer_type(OptimizerType::LevenbergMarquardt)
    .with_linear_solver_type(LinearSolverType::SparseCholesky)
    .with_max_iterations(100);

let mut solver = SolverFactory::create_solver(config);
```

## Data Directory

The `data/` directory contains test datasets in G2O, TORO, and other formats. These are used by examples and can be loaded using the `io` module functions.

## Code Quality Standards

This project follows comprehensive Rust best practices covering code organization, common patterns, performance, security, testing, pitfalls, and tooling.

### 1. Code Organization and Structure

#### Directory Structure
- **`src/`**: All Rust source code
  - **`main.rs`**: Binary crate entry point
  - **`lib.rs`**: Library crate entry point  
  - **`bin/`**: Multiple binary executables
  - **`modules/` or `components/`**: Group related modules
  - **`tests/`**: Integration tests
  - **`examples/`**: Example code
- **`benches/`**: Benchmark tests (using `criterion`)
- **`Cargo.toml`**: Project manifest (never manually edit `Cargo.lock`)

#### File Naming and Module Organization
- Use snake_case for file names (e.g., `my_module.rs`)
- Use modules to organize code into logical units
- Declare modules using `mod` keyword, `pub mod` for public modules
- Create separate files for each module for better readability
- Use `use` statements to bring items into scope

### 2. Common Patterns and Anti-patterns

#### Recommended Approaches
- **Data Structures**: Use `Vec` for dynamic arrays, `HashMap` for key-value pairs, `HashSet` for unique elements, `BTreeMap`/`BTreeSet` for sorted collections
- **Concurrency**: Use `Arc` and `Mutex` for shared mutable state, channels for message passing, `rayon` for data parallelism
- **Asynchronous Programming**: Use `async` and `await` for asynchronous code
- **Error Handling**: Use `Result` type for recoverable errors, `panic!` for unrecoverable errors

#### Anti-patterns to Avoid
- **Unnecessary Cloning**: Avoid cloning data unless absolutely necessary. Use references instead
- **Excessive `unwrap()` Calls**: Handle errors properly instead of using `unwrap()` which can cause panics
- **Overuse of `unsafe`**: Minimize unsafe code and carefully review any unsafe code
- **Ignoring Compiler Warnings**: Treat compiler warnings as errors and fix them
- **Premature Optimization**: Focus on writing clear, correct code first, then optimize if necessary

#### State Management
- **Immutability by Default**: Prefer immutable data structures and functions that return new values
- **Ownership and Borrowing**: Use Rust's ownership and borrowing system to manage memory and prevent data races
- **Interior Mutability**: Use `Cell`, `RefCell`, `Mutex`, and `RwLock` for interior mutability when necessary

#### Error Handling Patterns
- **`Result<T, E>`**: Use `Result` to represent fallible operations
- **`Option<T>`**: Use `Option` to represent the possibility of a missing value
- **`?` Operator**: Use the `?` operator to propagate errors up the call stack
- **Custom Error Types**: Define custom error types using enums or structs
- **`anyhow` and `thiserror` Crates**: Consider using `anyhow` for simple error handling and `thiserror` for custom error types

### 3. Performance Considerations

#### Optimization Techniques
- **Profiling**: Use profiling tools (`perf`, `cargo flamegraph`) to identify bottlenecks
- **Benchmarking**: Use benchmarking tools (`criterion`) to measure performance
- **Zero-Cost Abstractions**: Leverage Rust's zero-cost abstractions (iterators, closures, generics)
- **Inlining**: Use `#[inline]` attribute to encourage compiler inlining
- **LTO**: Enable Link-Time Optimization for better performance

#### Memory Management
- **Minimize Allocations**: Reduce allocations/deallocations by reusing memory and using stack allocation
- **Avoid Copying Large Data**: Use references or smart pointers instead of copying
- **Efficient Data Structures**: Choose the right data structure based on performance characteristics
- **Smart Pointers**: `Box` for single ownership heap allocation, `Rc`/`Arc` for shared ownership

#### Bundle Size Optimization
- **Strip Debug Symbols**: Remove debug symbols from release builds
- **Enable LTO**: Can reduce binary size by removing dead code
- **Use `minisize` Profile**: Create profile optimizing for size
- **Avoid Unnecessary Dependencies**: Only include absolutely necessary dependencies

### 4. Security Best Practices

#### Common Vulnerabilities Prevention
- **Buffer Overflows**: Use safe indexing methods (`get()`, `get_mut()`) and validate input sizes
- **SQL Injection**: Use parameterized queries and escape user input
- **Cross-Site Scripting (XSS)**: Escape user input when rendering HTML
- **Command Injection**: Avoid `std::process::Command` with user-supplied arguments
- **Integer Overflows**: Use `checked_add`, `checked_sub`, `checked_mul` methods
- **Data Races**: Use appropriate synchronization primitives (`Mutex`, `RwLock`, channels)

#### Input Validation
- **Validate All Input**: Validate input from external sources (user input, network data, files)
- **Whitelist Approach**: Define allowed values and reject non-matching input
- **Sanitize Input**: Remove or escape potentially dangerous characters
- **Limit Input Length**: Prevent buffer overflows by limiting string length
- **Check Data Types**: Ensure input data is of expected type

#### Data Protection
- **Encrypt Sensitive Data**: Use strong encryption (AES-256) for data at rest and in transit
- **Use HTTPS**: Encrypt client-server communication
- **Protect API Keys**: Store securely and restrict usage to authorized users
- **Handle Secrets Securely**: Use environment variables or dedicated secret management tools
- **Avoid Hardcoding Secrets**: Never hardcode secrets directly in code
- **Data Masking**: Mask or redact sensitive data in logs and displays

### 5. Testing Approaches

#### Unit Testing
- **Test Individual Units**: Write unit tests for functions, modules, and components
- **Use `#[test]` Attribute**: Mark functions as unit tests
- **Use Assertions**: Use `assert!` and `assert_eq!` macros to check behavior
- **Test Driven Development**: Consider writing tests before code
- **Table-Driven Tests**: Use parameterized tests for multiple scenarios

#### Integration and End-to-End Testing
- **Test Component Interactions**: Write integration tests for component interactions
- **Use `tests/` Directory**: Place integration tests in separate `tests/` directory
- **Test Entire Application**: Write end-to-end tests for complete application flows
- **Use Testing Frameworks**: Consider `cucumber`, `selenium` for automation

#### Test Organization and Mocking
- **Group by Functionality**: Organize tests into modules based on functionality
- **Descriptive Test Names**: Use clear names indicating what the test verifies
- **Keep Tests Separate**: Maintain separation between tests and production code
- **Run Tests Frequently**: Integrate tests into development workflow
- **Use Mocking Libraries**: Use `mockall`, `mockito` for mock objects
- **Use Traits for Interfaces**: Define traits to enable mocking and stubbing

### 6. Common Pitfalls and Gotchas

#### Frequent Mistakes
- **Borrowing Rules**: Misunderstanding ownership, borrowing, and lifetimes
- **Move Semantics**: Be aware of move semantics and ownership transfer
- **Lifetime Annotations**: Annotate lifetimes when necessary
- **Error Handling**: Handle errors properly instead of using `unwrap()`
- **Unsafe Code**: Minimize and carefully review unsafe code

#### Edge Cases and Compatibility
- **Integer Overflow**: Use checked arithmetic methods
- **Unicode Handling**: Handle Unicode characters correctly
- **File Paths**: Handle paths correctly across different operating systems
- **Concurrency**: Avoid data races and deadlocks in concurrent code
- **C Interoperability**: Be careful with C code interaction
- **Platform-Specific Code**: Use conditional compilation for platform differences

#### Debugging Strategies
- **Use `println!`**: Print debugging information
- **Use Debuggers**: Use `gdb`, `lldb` for stepping through code
- **Use Assertions**: Check code behavior with `assert!`
- **Use Logging**: Use logging libraries (`log`, `tracing`) for debug info
- **Use Clippy**: Catch common mistakes and improve code quality
- **Use `cargo-flamegraph`**: Profile and visualize code execution

### 7. Tooling and Environment

#### Development Tools
- **Rustup**: Manage Rust toolchains and versions
- **Cargo**: Package manager and build tool
- **IDE/Editor**: VS Code with rust-analyzer, IntelliJ Rust, or other Rust-supported editors
- **Clippy**: Linter for Rust code
- **Rustfmt**: Code formatter
- **Cargo-edit**: Easily modify `Cargo.toml` dependencies
- **Cargo-watch**: Automatically run tests on file changes

#### Build Configuration
```toml
[package]
name = "apex-solver"
version = "0.1.0"
edition = "2021"

[dependencies]
faer = { version = "0.22", features = ["sparse"] }
nalgebra = "0.33"
thiserror = "1.0"
rayon = "1.10"

[dev-dependencies]
criterion = "0.5"

[features]
default = []
visualization = ["rerun"]

[profile.release]
opt-level = 3
debug = false
lto = true
```

#### Linting and Formatting
- **Use Clippy**: Run `cargo clippy` to catch common mistakes
- **Use Rustfmt**: Run `cargo fmt` to format code according to style guide
- **Configure Editor**: Set up automatic Clippy and Rustfmt on save
- **Pre-commit Hooks**: Run linting and formatting before commits

#### Deployment and CI/CD
- **Build Release Binaries**: Use `cargo build --release` for optimized builds
- **Minimize Dependencies**: Reduce dependencies for smaller deployments
- **Containerization**: Use Docker for consistent deployment environments
- **Static Linking**: Create single executable files
- **CI/CD Integration**: Use GitHub Actions, GitLab CI, or Jenkins for automation
- **Run Tests on CI**: Automate unit, integration, and end-to-end tests
- **Enforce Standards**: Run linters and formatters on CI

### Important Reminders
- Minimize unsafe code usage
- Use `Result` types for error handling with `?` operator
- Follow ownership and borrowing patterns
- Prefer `cargo fmt` and `cargo clippy` for consistent formatting and linting
- Always run `cargo test`, `cargo clippy`, and `cargo fmt` before committing
- Focus on writing idiomatic, safe, and maintainable Rust code