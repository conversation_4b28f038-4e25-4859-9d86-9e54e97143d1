%% This BibTeX bibliography file was created using BibDesk.
%% http://bibdesk.sourceforge.net/

%% Created for <PERSON> at 2019-03-15 21:51:26 +0100


%% Saved with string encoding Unicode (UTF-8)

@misc{manif,
	Author = {<PERSON><PERSON><PERSON> and <PERSON>\`a},
	url = {https://github.com/artivis/manif},
	Month = {January},
	Read = {0},
	Title = {\texttt{manif}: a small {C++} header-only library for {L}ie theory.},
	Year = {2019},
}

@misc{Sophus,
	Author = {Hauke <PERSON>rasdat},
	Title = {Sophus},
	year = 2018,
	url = {https://github.com/strasdat/Sophus},
}

@inproceedings{wave_geometry,
  title={Manifold Geometry with Fast Automatic Derivatives and Coordinate Frame Semantics Checking in {C++}},
  author={<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={{15th Conference on Computer and Robot Vision (CRV)}},
  year={2018},
  Url = {https://github.com/wavelab/wave_geometry},
  Urldate = {2018-12-02},
	pages = {126-133},
	doi = {10.1109/CRV.2018.00027},
	publisher = {{IEEE} Computer Society},
	isbn = {978-1-5386-6481-0},
}

@misc{eigenweb,
	Author = {Ga\"{e}l Guennebaud and Beno\^{i}t Jacob and others},
	Title = {Eigen v3},
	Url = {http://eigen.tuxfamily.org},
	Year = {2010},
}

@Inbook{Koranne2011,
	author={Koranne, Sandeep},
	title={Boost C++ Libraries},
	bookTitle={Handbook of Open Source Tools},
	year={2011},
	publisher={Springer US},
	address={Boston, MA},
	pages={127--143},
	abstract="In this chapter we discuss the Boost C++ API. Boost is a peer-reviewed C++ class library which implements many interesting and useful data structures and algorithms. In particular we discuss the use of Boost smart pointers, Boost asynchronous IO, and IO Streams. Boost also implements many data structures which are not present in the C++ standard library (e.g. bimap). Boost Graph Library (BGL) is presented with the help of real-life example. We compare Boost multi-threading and memory pool performance to APR. We discuss the integration of Python with C++ using Boost. We conclude the chapter with a discussion of Boost Generic Image Processing Library.",
	isbn={978-1-4419-7719-9},
	doi={10.1007/978-1-4419-7719-9_6},
	url={https://doi.org/10.1007/978-1-4419-7719-9_6},}

@article{Sola18,
    author = {Sol{\`{a}}, Joan and Deray, Jeremie and Atchuthan, Dinesh},
    title = {{A micro Lie theory for state estimation in robotics}},
    archivePrefix = {arXiv},
    eprint = {1812.01537},
    primaryClass = {cs.RO},
    year = 2018,
    url = {https://arxiv.org/abs/1812.01537v4},
}

@techreport{EADE-Lie,
	Author = {Ethan Eade},
	Title = {Lie Groups for {2D} and {3D} Transformations},
  url = {http://ethaneade.com/lie.pdf},
  year = {2013},
  institution = {Cambridge University},}

@book{CHIRIKJIAN-11,
	Author = {Chirikjian, G.S.},
	Isbn = {9780817649449},
	Lccn = {2009933211},
	Publisher = {Birkh{\"a}user Boston},
	Series = {Applied and Numerical Harmonic Analysis},
	Title = {Stochastic Models, Information Theory, and {L}ie Groups, Volume 2: Analytic Methods and Modern Applications},
	Url = {https://books.google.ch/books?id=hZ1CAAAAQBAJ},
	Year = {2011},
}

@book{BARFOOT-17-Estimation,
	Author = {Barfoot, Timothy D.},
	Doi = {10.1017/9781316671528},
	Place = {Cambridge},
	Publisher = {Cambridge University Press},
	Title = {State Estimation for Robotics},
	Year = {2017},
}
