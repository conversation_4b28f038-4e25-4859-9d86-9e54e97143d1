# Core Components Relationship: ParameterBlock, Residual<PERSON>lock, and Problem

## Overview

The apex-solver optimization framework consists of three core components that work together to solve nonlinear least squares problems:

- **ParameterBlock**: Represents the variables being optimized
- **ResidualBlock**: Represents the constraints/measurements to be satisfied
- **Problem**: Orchestrates the optimization by managing both types of blocks

This document explains the fundamental differences between these components and how the `Problem` struct controls and coordinates them.

## Fundamental Differences

### ParameterBlock: The "What" (Variables to Optimize)

**Purpose**: Represents the optimization variables - the parameters we want to find optimal values for.

**Key Characteristics:**
- Contains actual parameter values (`na::DVector<f64>`)
- Manages constraints on individual parameters (bounds, fixed variables)
- Handles geometric constraints through manifolds
- Provides geometric operations for optimization updates

**Analogy**: Like the positions of pieces on a chessboard that we want to arrange optimally.

### ResidualBlock: The "How" (Constraints to Satisfy)

**Purpose**: Represents individual measurements or constraints that define what "optimal" means.

**Key Characteristics:**
- Contains residual functions (factors) that compute errors
- Defines relationships between variables
- Computes residuals and their derivatives (Jacobians)
- Handles robust estimation through loss functions

**Analogy**: Like the rules of chess that define legal moves and game objectives.

### Problem: The "Why" and "When" (Optimization Orchestrator)

**Purpose**: Manages the overall optimization problem and coordinates all components.

**Key Characteristics:**
- Contains collections of residual blocks
- Manages global parameter constraints and manifolds
- Orchestrates parallel computation
- Provides high-level optimization interface

**Analogy**: Like the chess game itself - manages all pieces and enforces all rules.

## Detailed Component Analysis

### ParameterBlock Deep Dive

```rust
#[derive(Clone)]
pub struct ParameterBlock {
    pub params: na::DVector<f64>,                    // The actual values
    pub fixed_variables: HashSet<usize>,             // Indices that can't change
    pub variable_bounds: HashMap<usize, (f64, f64)>, // Value constraints
    pub manifold: Option<Arc<dyn Manifold + Sync + Send>>, // Geometric constraints
}
```

**Responsibilities:**
1. **Value Storage**: Holds current parameter values
2. **Constraint Enforcement**: Applies bounds and fixed variable constraints
3. **Geometric Operations**: Handles manifold-based updates
4. **Dimension Management**: Tracks ambient vs tangent space dimensions

**Key Methods:**
- `plus_f64()`: Applies optimization updates geometrically correctly
- `y_minus_f64()`: Computes differences in tangent space
- `update_params()`: Applies constraints to proposed updates

### ResidualBlock Deep Dive

```rust
pub struct ResidualBlock {
    pub residual_block_id: usize,              // Unique identifier
    pub dim_residual: usize,                   // Output dimension
    pub residual_row_start_idx: usize,         // Position in global residual
    pub variable_key_list: Vec<String>,        // Which variables it uses
    pub factor: Box<dyn FactorImpl + Send>,    // The residual function
    pub loss_func: Option<Box<dyn Loss + Send>>, // Robust estimation
}
```

**Responsibilities:**
1. **Residual Computation**: Evaluates constraint satisfaction
2. **Jacobian Computation**: Computes derivatives for optimization
3. **Loss Function Application**: Handles outliers robustly
4. **Variable Relationship**: Defines which parameters interact

**Key Methods:**
- `residual_and_jacobian()`: Core computation method

### Problem Deep Dive

```rust
pub struct Problem {
    pub total_residual_dimension: usize,                    // Total residual size
    residual_id_count: usize,                              // ID counter
    residual_blocks: HashMap<ResidualBlockId, ResidualBlock>, // All constraints
    pub fixed_variable_indexes: HashMap<String, HashSet<usize>>, // Global fixed vars
    pub variable_bounds: HashMap<String, HashMap<usize, (f64, f64)>>, // Global bounds
    pub variable_manifold: HashMap<String, Arc<dyn Manifold + Sync + Send>>, // Global manifolds
}
```

**Responsibilities:**
1. **Block Management**: Creates and manages residual blocks
2. **Parameter Management**: Creates parameter blocks with constraints
3. **Global Coordination**: Orchestrates parallel computation
4. **Constraint Propagation**: Applies global constraints to parameters

## How Problem Controls and Checks the Components

### 1. Creation and Setup Phase

#### Creating ResidualBlocks
```rust
// Problem creates and manages residual blocks
let residual_id = problem.add_residual_block(
    3,                          // 3D residual (x, y, theta difference)
    &["pose1", "pose2"],        // Variables involved
    Box::new(relative_pose_factor), // The constraint function
    Some(Box::new(huber_loss)), // Robust loss function
);
```

**Problem's Control:**
- Assigns unique IDs to residual blocks
- Tracks total residual dimension
- Manages residual block lifecycle (add/remove)

#### Setting Global Constraints
```rust
// Problem manages global parameter constraints
problem.fix_variable("world_frame", 0);     // Fix x position
problem.fix_variable("world_frame", 1);     // Fix y position
problem.fix_variable("world_frame", 2);     // Fix orientation

problem.set_variable_bounds("sensor_range", 0, 0.0, 100.0); // Range limits

problem.set_variable_manifold("robot_pose", Arc::new(SE2Manifold {}));
```

**Problem's Control:**
- Maintains global constraint dictionaries
- Applies constraints when creating parameter blocks
- Validates constraint consistency

### 2. Parameter Block Creation and Initialization

#### Creating ParameterBlocks from Initial Values
```rust
// Problem creates parameter blocks with applied constraints
let parameter_blocks = problem.initialize_parameter_blocks(&initial_values);
```

**Problem's Control:**
1. Creates `ParameterBlock` instances from initial values
2. Applies global fixed variable constraints
3. Applies global bounds constraints
4. Applies global manifold constraints
5. Returns configured parameter blocks ready for optimization

### 3. Computation Coordination

#### Parallel Residual and Jacobian Computation
```rust
// Problem orchestrates parallel computation
let (total_residual, total_jacobian) = problem.compute_residual_and_jacobian(
    &parameter_blocks,
    &variable_to_column_mapping
);
```

**Problem's Control:**
1. **Parallel Processing**: Uses Rayon to process residual blocks in parallel
2. **Memory Management**: Allocates global residual and Jacobian matrices
3. **Result Accumulation**: Thread-safely combines results from all residual blocks
4. **Indexing Management**: Manages proper placement of results in global matrices

## Comprehensive Example: 2D Robot Localization

Let's illustrate with a complete example of robot pose estimation using odometry and landmark measurements.

### Problem Setup

```rust
use std::collections::HashMap;
use std::sync::Arc;
use nalgebra as na;
use apex_solver::*;

// Create the main problem
let mut problem = Problem::new();

// Define initial robot poses (what we want to optimize)
let mut initial_poses = HashMap::new();
initial_poses.insert("pose0".to_string(), na::DVector::from_vec(vec![0.0, 0.0, 0.0]));    // World frame
initial_poses.insert("pose1".to_string(), na::DVector::from_vec(vec![1.0, 0.0, 0.1]));    // After move 1
initial_poses.insert("pose2".to_string(), na::DVector::from_vec(vec![2.1, 0.2, 0.3]));    // After move 2

// Define landmark positions (what we want to optimize)
let mut initial_landmarks = HashMap::new();
initial_landmarks.insert("landmark_A".to_string(), na::DVector::from_vec(vec![1.5, 1.0]));   // Observed landmark
initial_landmarks.insert("landmark_B".to_string(), na::DVector::from_vec(vec![2.8, 0.5]));   // Another landmark

// Combine all variables
let mut initial_values = HashMap::new();
initial_values.extend(initial_poses);
initial_values.extend(initial_landmarks);
```

### Setting Global Constraints

```rust
// Fix the world frame (ParameterBlock constraint applied globally)
problem.fix_variable("pose0", 0);  // Fix x position
problem.fix_variable("pose0", 1);  // Fix y position
problem.fix_variable("pose0", 2);  // Fix orientation

// Set bounds on landmark positions (realistic world limits)
problem.set_variable_bounds("landmark_A", 0, -50.0, 50.0);  // x bounds
problem.set_variable_bounds("landmark_A", 1, -50.0, 50.0);  // y bounds
problem.set_variable_bounds("landmark_B", 0, -50.0, 50.0);  // x bounds
problem.set_variable_bounds("landmark_B", 1, -50.0, 50.0);  // y bounds

// Set geometric manifolds for poses (SE(2) for 2D rigid transformations)
let se2_manifold = Arc::new(SE2Manifold {});
problem.set_variable_manifold("pose0", se2_manifold.clone());
problem.set_variable_manifold("pose1", se2_manifold.clone());
problem.set_variable_manifold("pose2", se2_manifold.clone());
```

### Adding Residual Blocks (Constraints)

```rust
// Odometry constraint between pose0 and pose1
let odometry_factor_01 = OdometryFactor2D {
    measured_translation: na::Vector2::new(1.0, 0.0),
    measured_rotation: 0.1,
    information_matrix: na::Matrix3::identity() * 10.0, // High confidence
};

let odometry_id_01 = problem.add_residual_block(
    3,                                    // 3D residual (x, y, theta)
    &["pose0", "pose1"],                  // Variables involved
    Box::new(odometry_factor_01),         // The constraint
    Some(Box::new(HuberLoss::new(1.0))),  // Robust to outliers
);

// Odometry constraint between pose1 and pose2
let odometry_factor_12 = OdometryFactor2D {
    measured_translation: na::Vector2::new(1.1, 0.2),
    measured_rotation: 0.2,
    information_matrix: na::Matrix3::identity() * 10.0,
};

let odometry_id_12 = problem.add_residual_block(
    3,
    &["pose1", "pose2"],
    Box::new(odometry_factor_12),
    Some(Box::new(HuberLoss::new(1.0))),
);

// Landmark observation from pose1
let landmark_factor_1A = LandmarkFactor2D {
    measured_bearing: 0.8,                // Observed angle to landmark
    measured_range: 1.2,                  // Observed distance to landmark
    information_matrix: na::Matrix2::identity() * 5.0, // Medium confidence
};

let landmark_id_1A = problem.add_residual_block(
    2,                                    // 2D residual (bearing, range)
    &["pose1", "landmark_A"],             // Robot pose and landmark position
    Box::new(landmark_factor_1A),
    Some(Box::new(CauchyLoss::new(0.5))), // Robust to outliers
);

// Landmark observation from pose2
let landmark_factor_2B = LandmarkFactor2D {
    measured_bearing: 0.1,
    measured_range: 0.8,
    information_matrix: na::Matrix2::identity() * 5.0,
};

let landmark_id_2B = problem.add_residual_block(
    2,
    &["pose2", "landmark_B"],
    Box::new(landmark_factor_2B),
    Some(Box::new(CauchyLoss::new(0.5))),
);
```

### Problem's Control: Creating Parameter Blocks

```rust
// Problem creates ParameterBlocks with all constraints applied
let parameter_blocks = problem.initialize_parameter_blocks(&initial_values);

// This creates:
// - ParameterBlock for "pose0": SE(2) manifold, all indices fixed
// - ParameterBlock for "pose1": SE(2) manifold, no fixed variables
// - ParameterBlock for "pose2": SE(2) manifold, no fixed variables
// - ParameterBlock for "landmark_A": Euclidean, bounds applied
// - ParameterBlock for "landmark_B": Euclidean, bounds applied
```

### Problem's Control: Orchestrating Computation

```rust
// Problem creates variable-to-column mapping for Jacobian
let variable_to_column = problem.get_variable_name_to_col_idx_dict(&parameter_blocks);

// Problem orchestrates parallel computation of all residuals and Jacobians
let (total_residual, total_jacobian) = problem.compute_residual_and_jacobian(
    &parameter_blocks,
    &variable_to_column
);

// This:
// 1. Processes all 4 residual blocks in parallel
// 2. Accumulates 3D + 3D + 2D + 2D = 10D total residual
// 3. Builds Jacobian with proper column placement
// 4. Returns results for optimization algorithms
```

## Key Control Mechanisms

### 1. Constraint Propagation
- **Problem** defines global constraints
- **Problem** applies them when creating **ParameterBlocks**
- **ParameterBlocks** enforce constraints during updates

### 2. Parallel Coordination
- **Problem** manages thread-safe result accumulation
- **ResidualBlocks** compute independently in parallel
- **Problem** combines results into global matrices

### 3. Geometric Consistency
- **Problem** assigns manifolds to variables
- **ParameterBlocks** use manifolds for geometric operations
- **ResidualBlocks** work in appropriate tangent spaces

### 4. Memory Management
- **Problem** tracks total dimensions and indexing
- **ResidualBlocks** know their position in global residual
- **ParameterBlocks** manage their own memory efficiently

## Optimization Workflow

1. **Setup Phase**: Problem creates ResidualBlocks and applies global constraints
2. **Initialization Phase**: Problem creates ParameterBlocks with constraints
3. **Computation Phase**: Problem orchestrates parallel residual/Jacobian computation
4. **Optimization Phase**: External optimizer uses results to update ParameterBlocks
5. **Update Phase**: ParameterBlocks apply constraints to optimization updates
6. **Iteration**: Repeat computation and optimization until convergence

## Summary of Relationships

| Component | Role | Controlled By | Controls |
|-----------|------|---------------|----------|
| **ParameterBlock** | Variable representation with constraints | Problem (constraint application) | Own geometric operations |
| **ResidualBlock** | Constraint representation | Problem (creation, parallel execution) | Residual/Jacobian computation |
| **Problem** | Orchestrator and coordinator | User (high-level API) | ParameterBlocks, ResidualBlocks, parallel execution |

The **Problem** acts as the central coordinator that:
- **Creates** and manages the lifecycle of both types of blocks
- **Applies** global constraints to ParameterBlocks
- **Orchestrates** parallel computation across ResidualBlocks
