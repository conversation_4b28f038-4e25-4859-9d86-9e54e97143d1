# ResidualBlock Struct Documentation

## Overview

The `ResidualBlock` struct represents an individual constraint or measurement in the optimization problem. It encapsulates the relationship between variables through a residual function, optional loss function for robust estimation, and manages the computation of residuals and their Jacobians. Each residual block contributes to the overall objective function that the optimizer minimizes.

## ResidualBlock Struct Fields

### Core Fields

```rust
pub struct ResidualBlock {
    pub residual_block_id: usize,
    pub dim_residual: usize,
    pub residual_row_start_idx: usize,
    pub variable_key_list: Vec<String>,
    pub factor: Box<dyn FactorImpl + Send>,
    pub loss_func: Option<Box<dyn Loss + Send>>,
}
```

#### `residual_block_id: usize`
- **Purpose**: Unique identifier for this residual block
- **Usage**: Used for tracking and managing residual blocks in the Problem struct
- **Assignment**: Set during construction, typically sequential

#### `dim_residual: usize`
- **Purpose**: Dimension of the residual vector produced by this block
- **Usage**: Determines the size of residual and Jacobian contributions
- **Example**: 2D pose constraint might have dim_residual = 3 (x, y, theta differences)

#### `residual_row_start_idx: usize`
- **Purpose**: Starting row index in the global residual vector
- **Usage**: Specifies where this block's residual contributes in the total residual vector
- **Importance**: Enables proper accumulation of residuals from multiple blocks

#### `variable_key_list: Vec<String>`
- **Purpose**: List of variable names that this residual block depends on
- **Usage**: Identifies which parameters affect this constraint
- **Example**: `["pose1", "pose2"]` for a relative pose measurement between two poses

#### `factor: Box<dyn FactorImpl + Send>`
- **Purpose**: The core residual function implementation
- **Usage**: Computes the residual vector and Jacobian matrix
- **Interface**: Must implement `FactorImpl` trait with `residual_with_jacobian` method
- **Thread Safety**: Wrapped in `Box<dyn ... + Send>` for parallel computation

#### `loss_func: Option<Box<dyn Loss + Send>>`
- **Purpose**: Optional robust loss function for outlier handling
- **Usage**: Applies robust estimation to reduce influence of outliers
- **Examples**: Huber loss, Cauchy loss, or None for standard least squares
- **Thread Safety**: Wrapped in `Box<dyn ... + Send>` for parallel computation

## ResidualBlock Methods

### Constructor

#### `new(residual_block_id: usize, dim_residual: usize, residual_row_start_idx: usize, variable_key_size_list: &[&str], factor: Box<dyn FactorImpl + Send>, loss_func: Option<Box<dyn Loss + Send>>) -> Self`

Creates a new residual block with the specified parameters.

**Parameters:**
- `residual_block_id`: Unique identifier for the block
- `dim_residual`: Dimension of the residual vector
- `residual_row_start_idx`: Starting row index in global residual vector
- `variable_key_size_list`: Slice of variable names as string slices
- `factor`: The residual function implementation
- `loss_func`: Optional loss function for robust estimation

**Returns:** New `ResidualBlock` instance

**Effects on struct:**
- Initializes all fields with provided values
- Converts string slices to owned Strings for `variable_key_list`
- Stores the factor and optional loss function

### Core Computation

#### `residual_and_jacobian(&self, params: &[&ParameterBlock]) -> (na::DVector<f64>, na::DMatrix<f64>)`

Computes the residual vector and Jacobian matrix for this residual block.

**Parameters:**
- `params`: Slice of parameter blocks corresponding to `variable_key_list`

**Returns:** Tuple of (residual_vector, jacobian_matrix)

**Algorithm:**
1. Extract parameter values from parameter blocks
2. Call factor's `residual_with_jacobian` method
3. Apply loss function correction if present:
   - Compute squared norm of residual
   - Evaluate loss function to get rho values
   - Create corrector with rho values
   - Apply corrections to Jacobian and residual

**Effects on struct:** None (read-only method)

## Loss Function Correction Process

When a loss function is present, the residual and Jacobian undergo correction:

### Step 1: Loss Function Evaluation
```rust
let squared_norm = residual.norm_squared();
let rho = loss_func.evaluate(squared_norm);
```

The loss function takes the squared norm of the residual and returns a `Rho` struct containing:
- `rho[0]`: Loss function value
- `rho[1]`: First derivative w.r.t. squared norm
- `rho[2]`: Second derivative w.r.t. squared norm

### Step 2: Corrector Creation
```rust
let corrector = Corrector::new(squared_norm, &rho);
```

The `Corrector` encapsulates the loss function derivatives and provides methods to apply corrections.

### Step 3: Jacobian Correction
```rust
corrector.correct_jacobian(&residual, &mut jacobian);
```

Applies the loss function correction to the Jacobian matrix using the chain rule.

### Step 4: Residual Correction
```rust
corrector.correct_residuals(&mut residual);
```

Applies the loss function correction to the residual vector.

## Helper Functions

### `get_variable_rows(variable_rows: &[usize]) -> Vec<Vec<usize>>`

Utility function to convert a list of variable sizes into row ranges.

**Parameters:**
- `variable_rows`: Slice of variable sizes (dimensions)

**Returns:** Vector of vectors containing row indices for each variable

**Example:**
```rust
let sizes = &[3, 2, 4]; // Variables with dimensions 3, 2, 4
let ranges = get_variable_rows(sizes);
// Result: [[0, 1, 2], [3, 4], [5, 6, 7, 8]]
```

**Algorithm:**
1. Initialize empty result vector
2. Iterate through variable sizes
3. Create range for each variable starting from current offset
4. Update offset for next variable

## Example Usage

### Basic Residual Block Creation

```rust
use apex_solver::ResidualBlock;
use apex_solver::factors::FactorImpl;
use apex_solver::loss_functions::Loss;

// Define a simple factor (residual function)
struct SimpleFactor;
impl FactorImpl for SimpleFactor {
    fn residual_with_jacobian(&self, params: &[na::DVector<f64>])
        -> (na::DVector<f64>, na::DMatrix<f64>) {
        // Implementation would compute residual and Jacobian
        // based on the parameter values
        todo!()
    }
}

// Create residual block
let factor = Box::new(SimpleFactor {});
let loss_func = None; // No robust loss function

let residual_block = ResidualBlock::new(
    0,                    // residual_block_id
    3,                    // dim_residual (3D measurement)
    0,                    // residual_row_start_idx
    &["pose1", "pose2"],  // variables involved
    factor,
    loss_func,
);
```

### With Loss Function (Robust Estimation)

```rust
use apex_solver::loss_functions::HuberLoss;

// Create Huber loss function for outlier robustness
let huber_loss = HuberLoss::new(1.0); // delta parameter
let loss_func = Some(Box::new(huber_loss) as Box<dyn Loss + Send>);

// Create residual block with robust loss
let residual_block = ResidualBlock::new(
    1,
    2, // 2D residual
    3, // starts at row 3 in global residual
    &["landmark", "camera"],
    factor,
    loss_func,
);
```

### Computing Residual and Jacobian

```rust
use apex_solver::ParameterBlock;

// Create parameter blocks
let pose1_params = ParameterBlock::from_vec(vec![0.0, 0.0, 0.0]); // x, y, theta
let pose2_params = ParameterBlock::from_vec(vec![1.0, 0.0, 0.5]);

let params = vec![&pose1_params, &pose2_params];

// Compute residual and Jacobian
let (residual, jacobian) = residual_block.residual_and_jacobian(&params);

// residual: DVector<f64> with dim_residual elements
// jacobian: DMatrix<f64> with shape (dim_residual, total_param_dims)
```

## Key Concepts

### Factor Interface
- **Purpose**: Defines the mathematical relationship between variables
- **Examples**: Relative pose constraints, landmark observations, IMU measurements
- **Requirements**: Must implement `residual_with_jacobian` method
- **Thread Safety**: Must be Send for parallel computation

### Loss Functions
- **Purpose**: Provide robustness against outliers in measurements
- **Common Types**:
  - **None**: Standard least squares (sensitive to outliers)
  - **Huber Loss**: Quadratic near zero, linear far from zero
  - **Cauchy Loss**: Smooth, gradually reduces outlier influence
  - **Tukey Loss**: Hard rejection of outliers beyond threshold

### Jacobian Matrix Structure
- **Shape**: `(dim_residual, total_parameter_dimensions)`
- **Layout**: Columns correspond to parameters in `variable_key_list` order
- **Usage**: Used by optimization algorithms to compute parameter updates
- **Sparsity**: Typically sparse, with zeros for unrelated parameters

### Parallel Computation
- **Thread Safety**: All components must implement `Send`
- **Boxed Traits**: Factor and loss function stored in Boxes for dynamic dispatch
- **Parameter Passing**: Parameter blocks passed by reference for efficiency

### Memory Management
- **Owned Strings**: Variable names stored as owned Strings
- **Boxed Traits**: Factor and loss function boxed to allow different implementations
- **Reference Parameters**: Parameter blocks passed by reference to avoid copying

## Integration with Problem Struct

The `ResidualBlock` is managed by the `Problem` struct:

1. **Creation**: `Problem::add_residual_block()` creates and stores residual blocks
2. **Computation**: `Problem::compute_residual_and_jacobian()` calls `residual_and_jacobian()` on each block
3. **Parallel Processing**: Rayon parallel iterator processes blocks concurrently
4. **Result Accumulation**: Individual results combined into global residual and Jacobian

This modular design allows for flexible problem construction while maintaining computational efficiency through parallel processing and proper memory management.

## Performance Considerations

- **Memory Layout**: Residual and Jacobian dimensions should match factor implementation
- **Computation Cost**: Factor evaluation is typically the most expensive operation
- **Loss Function Overhead**: Additional computation when robust estimation is used
- **Parallel Scalability**: Performance improves with more residual blocks and CPU cores
- **Cache Efficiency**: Parameter blocks accessed by reference to minimize memory movement

This documentation provides a comprehensive understanding of how `ResidualBlock` implements individual constraints in the optimization framework, from basic construction to advanced robust estimation techniques.
