# Problem Struct Documentation

## Overview

The `Problem` struct is the core component of the optimization framework in the apex-solver library. It represents an optimization problem consisting of residual blocks, variables, and their relationships. The problem is formulated as a nonlinear least squares optimization where we minimize the sum of squared residuals subject to various constraints and bounds.

## Problem Struct Fields

### Core Fields

```rust
pub struct Problem {
    pub total_residual_dimension: usize,
    residual_id_count: usize,
    residual_blocks: HashMap<ResidualBlockId, residual_block::ResidualBlock>,
    pub fixed_variable_indexes: HashMap<String, HashSet<usize>>,
    pub variable_bounds: HashMap<String, HashMap<usize, (f64, f64)>>,
    pub variable_manifold: HashMap<String, Arc<dyn Manifold + Sync + Send>>,
}
```

#### `total_residual_dimension: usize`
- **Purpose**: Tracks the total dimension of all residual vectors combined
- **Usage**: Used to allocate memory for residual vectors and Jacobian matrices
- **Updated**: Incremented when adding residual blocks, decremented when removing them

#### `residual_id_count: usize`
- **Purpose**: Counter for generating unique IDs for residual blocks
- **Usage**: Ensures each residual block has a unique identifier
- **Behavior**: Incremented each time a new residual block is added

#### `residual_blocks: HashMap<ResidualBlockId, residual_block::ResidualBlock>`
- **Purpose**: Stores all residual blocks that define the optimization problem
- **Key**: `ResidualBlockId` (usize) - unique identifier for each block
- **Value**: `ResidualBlock` - contains the residual function, variables, and loss function
- **Usage**: Core storage for the optimization problem definition

#### `fixed_variable_indexes: HashMap<String, HashSet<usize>>`
- **Purpose**: Specifies which variables should be held constant during optimization
- **Key**: Variable name (String)
- **Value**: Set of indices within the variable that should be fixed
- **Usage**: Prevents certain parameters from being modified during optimization

#### `variable_bounds: HashMap<String, HashMap<usize, (f64, f64)>>`
- **Purpose**: Defines upper and lower bounds for variables
- **Key**: Variable name (String)
- **Value**: Map of index to (lower_bound, upper_bound) pairs
- **Usage**: Constrains the optimization search space

#### `variable_manifold: HashMap<String, Arc<dyn Manifold + Sync + Send>>`
- **Purpose**: Associates variables with their geometric manifolds
- **Key**: Variable name (String)
- **Value**: Manifold implementation defining the variable's geometry
- **Usage**: Enables proper handling of constrained variables (e.g., rotations, poses)

## Problem Methods

### Constructor

#### `new() -> Problem`
Creates a new empty problem instance with default values.

```rust
pub fn new() -> Problem {
    Problem {
        total_residual_dimension: 0,
        residual_id_count: 0,
        residual_blocks: HashMap::new(),
        fixed_variable_indexes: HashMap::new(),
        variable_bounds: HashMap::new(),
        variable_manifold: HashMap::new(),
    }
}
```

**Effects on struct:**
- Initializes all fields to empty/default states
- Sets up the problem for adding residual blocks and variables

### Variable Management

#### `get_variable_name_to_col_idx_dict(&self, parameter_blocks: &HashMap<String, ParameterBlock>) -> HashMap<String, usize>`

Creates a mapping from variable names to their column indices in the Jacobian matrix.

**Parameters:**
- `parameter_blocks`: Map of variable names to their parameter blocks

**Returns:** HashMap mapping variable names to column indices

**Effects on struct:** None (read-only method)

**Algorithm:**
1. Iterates through parameter blocks in order
2. Assigns sequential column indices based on tangent space dimensions
3. Returns the mapping for Jacobian construction

#### `fix_variable(&mut self, var_to_fix: &str, idx: usize)`

Marks a specific index of a variable as fixed (constant) during optimization.

**Parameters:**
- `var_to_fix`: Name of the variable to fix
- `idx`: Index within the variable to fix

**Effects on struct:**
- Adds the index to `fixed_variable_indexes` for the specified variable
- Creates new HashSet if variable not previously fixed

#### `unfix_variable(&mut self, var_to_unfix: &str)`

Removes all fixed constraints for a variable, allowing it to be optimized.

**Parameters:**
- `var_to_unfix`: Name of the variable to unfix

**Effects on struct:**
- Removes the variable entry from `fixed_variable_indexes`
- Variable becomes fully optimizable

### Bounds Management

#### `set_variable_bounds(&mut self, var_to_bound: &str, idx: usize, lower_bound: f64, upper_bound: f64)`

Sets upper and lower bounds for a specific variable index.

**Parameters:**
- `var_to_bound`: Variable name
- `idx`: Index within the variable
- `lower_bound`: Lower bound value
- `upper_bound`: Upper bound value

**Effects on struct:**
- Adds bounds to `variable_bounds` map
- Validates that lower_bound ≤ upper_bound
- Logs error if bounds are invalid

#### `remove_variable_bounds(&mut self, var_to_unbound: &str)`

Removes all bounds constraints for a variable.

**Parameters:**
- `var_to_unbound`: Variable name to remove bounds from

**Effects on struct:**
- Removes variable entry from `variable_bounds`
- Variable becomes unbounded

### Manifold Management

#### `set_variable_manifold(&mut self, var_name: &str, manifold: Arc<dyn Manifold + Sync + Send>)`

Associates a geometric manifold with a variable for proper constraint handling.

**Parameters:**
- `var_name`: Variable name
- `manifold`: Manifold implementation

**Effects on struct:**
- Adds manifold to `variable_manifold` map
- Enables geometric constraints during optimization

### Residual Block Management

#### `add_residual_block(&mut self, dim_residual: usize, variable_key_size_list: &[&str], factor: Box<dyn factors::Factor + Send>, loss_func: Option<Box<dyn loss_functions::Loss + Send>>) -> ResidualBlockId`

Adds a new residual block to the optimization problem.

**Parameters:**
- `dim_residual`: Dimension of the residual vector
- `variable_key_size_list`: List of variable names involved
- `factor`: Residual function implementation
- `loss_func`: Optional loss function for robust estimation

**Returns:** Unique ID of the created residual block

**Effects on struct:**
- Creates new `ResidualBlock` and adds to `residual_blocks`
- Increments `residual_id_count`
- Updates `total_residual_dimension` by adding `dim_residual`

#### `remove_residual_block(&mut self, block_id: ResidualBlockId) -> Option<residual_block::ResidualBlock>`

Removes a residual block from the optimization problem.

**Parameters:**
- `block_id`: ID of the block to remove

**Returns:** The removed `ResidualBlock` if it existed

**Effects on struct:**
- Removes block from `residual_blocks`
- Decrements `total_residual_dimension` by the block's residual dimension
- Returns the removed block for potential reuse

### Parameter Initialization

#### `initialize_parameter_blocks(&self, initial_values: &HashMap<String, na::DVector<f64>>) -> HashMap<String, ParameterBlock>`

Creates parameter blocks from initial values, applying all configured constraints.

**Parameters:**
- `initial_values`: Map of variable names to initial value vectors

**Returns:** Map of variable names to initialized `ParameterBlock`s

**Effects on struct:** None (read-only method)

**Algorithm:**
1. Creates `ParameterBlock` for each initial value
2. Applies fixed variable constraints
3. Applies bounds constraints
4. Applies manifold constraints
5. Returns configured parameter blocks

### Computation

#### `compute_residual_and_jacobian(&self, parameter_blocks: &HashMap<String, ParameterBlock>, variable_name_to_col_idx_dict: &HashMap<String, usize>) -> (faer::Mat<f64>, na::DMatrix<f64>)`

Computes the total residual vector and Jacobian matrix for the entire problem.

**Parameters:**
- `parameter_blocks`: Current parameter values
- `variable_name_to_col_idx_dict`: Variable-to-column mapping

**Returns:** Tuple of (residual_vector, jacobian_matrix)

**Effects on struct:** None (read-only method)

**Algorithm:**
1. Allocates total residual vector and Jacobian matrix
2. Parallel computation across residual blocks:
   - Computes residual and Jacobian for each block
   - Accumulates results into total structures
3. Returns combined residual and Jacobian

**Parallel Processing:**
- Uses Rayon for parallel computation across residual blocks
- Thread-safe accumulation using `Arc<Mutex<>>`
- Each thread handles one residual block independently

## Example Usage

```rust
use std::collections::HashMap;
use std::sync::Arc;
use nalgebra as na;
use apex_solver::{Problem, ParameterBlock};
use apex_solver::factors::Factor;
use apex_solver::loss_functions::Loss;

// Create a simple 2D pose estimation problem
let mut problem = Problem::new();

// Define variables (robot poses)
let mut initial_values = HashMap::new();
initial_values.insert("pose1".to_string(), na::DVector::from_vec(vec![0.0, 0.0, 0.0])); // x, y, theta
initial_values.insert("pose2".to_string(), na::DVector::from_vec(vec![1.0, 0.0, 0.0]));

// Add a residual block for relative pose constraint
// This represents a measurement between pose1 and pose2
let factor = /* Some Factor implementation */;
let residual_id = problem.add_residual_block(
    3, // 3D residual (x, y, theta difference)
    &["pose1", "pose2"], // Variables involved
    Box::new(factor),
    None, // No loss function
);

// Fix the first pose (anchor the coordinate frame)
problem.fix_variable("pose1", 0); // Fix x
problem.fix_variable("pose1", 1); // Fix y
problem.fix_variable("pose1", 2); // Fix theta

// Set bounds on the second pose
problem.set_variable_bounds("pose2", 0, -10.0, 10.0); // x bounds
problem.set_variable_bounds("pose2", 1, -10.0, 10.0); // y bounds

// Initialize parameter blocks with constraints applied
let parameter_blocks = problem.initialize_parameter_blocks(&initial_values);

// Create variable-to-column mapping for Jacobian
let var_to_col = problem.get_variable_name_to_col_idx_dict(&parameter_blocks);

// Compute residual and Jacobian
let (residual, jacobian) = problem.compute_residual_and_jacobian(&parameter_blocks, &var_to_col);

// The residual and jacobian can now be used by an optimizer
// to minimize the sum of squared residuals
```

## Key Concepts

### Residual Blocks
- Represent individual constraints or measurements
- Define relationships between variables
- Contribute to the total objective function

### Parameter Blocks
- Encapsulate variable values and their properties
- Handle fixed variables, bounds, and manifolds
- Provide tangent space operations for optimization

### Manifolds
- Handle geometrically constrained variables
- Examples: SO(2), SO(3), SE(2), SE(3) for rotations and poses
- Ensure variables stay on valid geometric surfaces

### Jacobian Matrix
- Sparse matrix representing partial derivatives
- Columns correspond to variables, rows to residual dimensions
- Used by optimization algorithms (Gauss-Newton, Levenberg-Marquardt, etc.)

### Parallel Computation
- Residual blocks are processed in parallel using Rayon
- Thread-safe accumulation of results
- Scales efficiently with problem size and CPU cores

This documentation provides a comprehensive understanding of how the `Problem` struct manages optimization problems in the apex-solver library, from basic setup to advanced constraint handling and parallel computation.
