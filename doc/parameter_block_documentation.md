# Variable Struct Documentation

## Overview

The `Variable` struct represents a set of optimization variables with associated constraints and geometric properties. It encapsulates variable values, fixed indices, bounds, and manifolds to provide a unified interface for variable management in the optimization framework. This struct bridges the gap between raw variable vectors and geometrically constrained optimization variables.

## Variable Struct Fields

### Core Fields

```rust
#[derive(Clone)]
pub struct Variable {
    pub values: na::DVector<f64>,
    pub fixed_indices: HashSet<usize>,
    pub bounds: HashMap<usize, (f64, f64)>,
}
```

#### `values: na::DVector<f64>`
- **Purpose**: Stores the actual variable values as a column vector
- **Usage**: Contains the current state of the optimization variables
- **Dimension**: Can be ambient space or manifold representation depending on context
- **Example**: For a 2D pose, might contain `[x, y, theta]` values

#### `fixed_indices: HashSet<usize>`
- **Purpose**: Set of variable indices that should remain constant during optimization
- **Usage**: Prevents specified variables from being modified by the optimizer
- **Example**: Fix the first pose in a SLAM problem to anchor the coordinate frame
- **Application**: Applied during `update_values()` to maintain fixed values

#### `bounds: HashMap<usize, (f64, f64)>`
- **Purpose**: Defines upper and lower bounds for individual variables
- **Key**: Variable index (usize)
- **Value**: Tuple of (lower_bound, upper_bound)
- **Usage**: Constrains the optimization search space
- **Application**: Bounds are enforced during variable updates

## Variable Methods

### Constructor

#### `from_vec(values: na::DVector<f64>) -> Self`

Creates a new variable from a vector of variable values.

**Parameters:**
- `values`: Initial variable values as a nalgebra DVector

**Returns:** New `Variable` instance

**Effects on struct:**
- Initializes `values` with provided values
- Sets `fixed_indices` to empty set
- Sets `bounds` to empty map

### Dimension Queries

#### `ambient_size(&self) -> usize`

Returns the dimension of the ambient space containing the variables.

**Returns:** Number of variables in the ambient representation

**Notes:**
- This is always the dimension of `self.values`

#### `tangent_size(&self) -> usize`

Returns the dimension of the tangent space for optimization.

**Returns:** Dimension of the tangent space

**Algorithm:**
- Returns `ambient_size()` (Euclidean tangent space)

**Importance:**
- Determines the number of degrees of freedom for optimization
- Used for Jacobian matrix sizing and optimization variable counting

### Geometric Operations

#### `plus_f64(&self, dx: na::DVectorView<f64>) -> na::DVector<f64>`

Adds a tangent space vector to the current variables using vector addition.

**Parameters:**
- `dx`: Tangent space update vector (view to avoid copying)

**Returns:** Updated variable vector in ambient space

**Algorithm:**
- Use standard vector addition `self.values + dx`

**Usage:**
- Applies optimization updates to variables

#### `y_minus_f64(&self, y: na::DVectorView<f64>) -> na::DVector<f64>`

Computes the tangent space difference between a target point and current variables.

**Parameters:**
- `y`: Target variable vector in ambient space

**Returns:** Tangent space vector representing the difference

**Algorithm:**
- Use standard vector subtraction `y - self.values`

**Usage:**
- Computes residuals in tangent space for variables

### Variable Updates

#### `update_values(&mut self, mut new_values: na::DVector<f64>)`

Updates the variable values while respecting bounds and fixed indices.

**Parameters:**
- `new_values`: Proposed new variable values (mutable for in-place modification)

**Effects on struct:**
- Applies bounds: Clamps values to `[lower, upper]` for bounded variables
- Applies fixed indices: Restores original values for fixed variables
- Updates `self.values` with the corrected values

**Algorithm:**
1. **Bounds Application**: For each bounded variable index:
   ```rust
   new_values[idx] = new_values[idx].max(lower).min(upper);
   ```
2. **Fixed Variables Application**: For each fixed variable index:
   ```rust
   new_values[index_to_fix] = self.values[index_to_fix];
   ```
3. **Variable Update**: Set `self.values = new_values`

## Key Concepts

### Ambient Space vs Tangent Space

#### Ambient Space
- **Definition**: The space where parameters are represented for storage and computation
- **Example**: 4D quaternion for 3D rotation (SO(3) manifold)
- **Dimension**: Given by `ambient_size()`

#### Tangent Space
- **Definition**: The linear space where optimization occurs
- **Example**: 3D rotation vector for SO(3) (axis-angle representation)
- **Dimension**: Given by `tangent_size()`
- **Purpose**: Provides a linear approximation for optimization algorithms

### Manifold Operations

#### Plus Operation (`plus_f64`)
- **Purpose**: Adds a tangent space update to ambient space parameters
- **Example**: For SO(3): Applies rotation update to quaternion
- **Formula**: `x' = x ⊕ δx` where `⊕` is the manifold plus operation

#### Minus Operation (`minus_f64`)
- **Purpose**: Computes tangent space difference between two ambient points
- **Example**: For SO(3): Computes rotation difference as axis-angle
- **Formula**: `δx = y ⊖ x` where `⊖` is the manifold minus operation

### Constraints Handling

#### Fixed Variables
- **Purpose**: Prevent certain parameters from changing during optimization
- **Use Cases**: Anchor frames, known constants, calibration parameters
- **Implementation**: Values restored after each optimization update

#### Bounds
- **Purpose**: Constrain parameter values to valid ranges
- **Use Cases**: Physical limits, numerical stability, domain constraints
- **Implementation**: Values clamped to bounds during updates

## Example Usage

### Basic Variable Creation

```rust
use nalgebra as na;
use apex_solver::Variable;

// Create a 3D pose variable (x, y, theta)
let initial_pose = na::DVector::from_vec(vec![1.0, 2.0, 0.5]);
let mut pose_vars = Variable::from_vec(initial_pose);

// The variable is ready for use
assert_eq!(pose_vars.ambient_size(), 3);
assert_eq!(pose_vars.tangent_size(), 3);
```

### Applying Constraints

```rust
// Set bounds on position (indices 0 and 1)
pose_vars.bounds.insert(0, (-10.0, 10.0)); // x bounds
pose_vars.bounds.insert(1, (-10.0, 10.0)); // y bounds

// Fix the orientation (index 2)
pose_vars.fixed_indices.insert(2);

// Apply an update (this will respect bounds and fixed indices)
let proposed_update = na::DVector::from_vec(vec![15.0, -15.0, 1.5]); // Out of bounds + rotation
pose_vars.update_values(proposed_update);

// Result: x,y clamped to bounds, theta unchanged (fixed)
assert_eq!(pose_vars.values[0], 10.0);  // clamped to upper bound
assert_eq!(pose_vars.values[1], -10.0); // clamped to lower bound
assert_eq!(pose_vars.values[2], 0.5);   // unchanged (was fixed)
```

### Geometric Operations

```rust
// Current variables
let current_vars = Variable::from_vec(na::DVector::from_vec(vec![1.0, 0.0, 0.0]));

// Tangent space update (move +1 in x, rotate by 0.1 radians)
let tangent_update = na::DVector::from_vec(vec![1.0, 0.0, 0.1]);

// Apply update using vector operations
let new_vars = current_vars.plus_f64(tangent_update.as_view());

// Result: [2.0, 0.0, 0.1]

// Compute difference to target
let target_vars = na::DVector::from_vec(vec![2.0, 0.0, 0.0]);
let tangent_difference = current_vars.y_minus_f64(target_vars.as_view());

// tangent_difference represents how to get from current to target in tangent space
```

## Integration with Optimization

The `Variable` is used throughout the optimization framework:

1. **Problem Setup**: Created during `Problem::initialize_variables()`
2. **Constraint Application**: Bounds and fixed indices applied during optimization
3. **Geometric Operations**: Vector operations used in residual computation
4. **Variable Updates**: Updates applied while respecting all constraints

## Performance Considerations

- **Memory Efficiency**: Uses nalgebra's efficient vector operations
- **View Operations**: Uses `DVectorView` to avoid unnecessary copying
- **In-place Updates**: `update_values` modifies vectors in-place for efficiency

## Common Patterns

### Pose Graph Optimization
```rust
// Robot poses
for pose in robot_poses {
    let mut variable = Variable::from_vec(pose);
    // Add constraints as needed...
}
```

### Bundle Adjustment
```rust
// Camera parameters (usually Euclidean)
let camera_vars = Variable::from_vec(camera_vector);
// 3D points (Euclidean)
let point_vars = Variable::from_vec(point_vector);
// Add bounds if needed...
```

### Sensor Calibration
```rust
// Calibration parameters with bounds
let mut calib_vars = Variable::from_vec(initial_calib);
calib_vars.bounds.insert(0, (0.0, 100.0)); // sensor range
// Some parameters might be fixed if known...
```

This documentation provides a comprehensive understanding of how `Variable` manages optimization variables with constraints, bounds, and fixed indices in the apex-solver framework.
