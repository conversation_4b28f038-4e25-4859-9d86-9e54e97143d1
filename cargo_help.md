# Cargo Commands Reference

A comprehensive guide to all essential Cargo commands for Rust development.

## 🔨 Build and Compilation Commands

### Core Build Commands
- **`cargo build`** - Compile the project and all dependencies
  - `cargo build --release` - Build with optimizations
  - `cargo build --target <TARGET>` - Build for specific target platform
  - `cargo build --workspace` - Build all packages in workspace

- **`cargo check`** - Check code for errors without generating executable
  - `cargo check --all-targets` - Check all targets (lib, bins, tests, benches)
  - `cargo check --workspace` - Check all packages in workspace

- **`cargo rustc`** - Compile with extra Rust compiler options
  - `cargo rustc -- -C target-cpu=native` - Compile with CPU-specific optimizations

### Running and Testing
- **`cargo run`** - Compile and run the main binary
  - `cargo run --bin <NAME>` - Run specific binary
  - `cargo run --example <NAME>` - Run example
  - `cargo run --release` - Run optimized build

- **`cargo test`** - Run all tests
  - `cargo test <PATTERN>` - Run tests matching pattern
  - `cargo test --lib` - Run library tests only
  - `cargo test --integration` - Run integration tests only
  - `cargo test --doc` - Run documentation tests
  - `cargo test --workspace` - Test all packages in workspace

- **`cargo bench`** - Run benchmarks
  - `cargo bench <PATTERN>` - Run benchmarks matching pattern

## 📚 Documentation Commands

- **`cargo doc`** - Generate documentation
  - `cargo doc --open` - Generate and open documentation in browser
  - `cargo doc --no-deps` - Generate docs for current package only
  - `cargo doc --workspace` - Generate docs for all workspace packages

- **`cargo rustdoc`** - Build documentation with custom flags

## 🧹 Maintenance Commands

- **`cargo clean`** - Remove compiled artifacts
  - `cargo clean --release` - Clean release artifacts only
  - `cargo clean --target-dir <DIR>` - Clean specific target directory

- **`cargo update`** - Update dependencies to latest compatible versions
  - `cargo update <PACKAGE>` - Update specific package
  - `cargo update --dry-run` - Show what would be updated

- **`cargo fix`** - Automatically fix compiler warnings
  - `cargo fix --edition` - Fix edition-related issues
  - `cargo fix --edition-idioms` - Apply edition idioms

## 🔍 Code Quality and Analysis

### Formatting and Linting
- **`cargo fmt`** - Format code according to style guidelines
  - `cargo fmt --check` - Check if code is formatted
  - `cargo fmt --all` - Format all packages in workspace

- **`cargo clippy`** - Run Clippy linter for common mistakes
  - `cargo clippy --all-targets` - Run on all targets
  - `cargo clippy --all-features` - Run with all features enabled
  - `cargo clippy -- -D warnings` - Treat warnings as errors
  - `cargo clippy --fix` - Automatically fix lint suggestions

### Analysis Tools
- **`cargo tree`** - Display dependency tree
  - `cargo tree --invert <PACKAGE>` - Show reverse dependencies
  - `cargo tree --duplicates` - Show duplicate dependencies
  - `cargo tree --format <FORMAT>` - Custom output format

- **`cargo metadata`** - Output package metadata in JSON format

## 📦 Package Management Commands

### Project Creation
- **`cargo new <NAME>`** - Create new Rust project
  - `cargo new --lib <NAME>` - Create library project
  - `cargo new --bin <NAME>` - Create binary project (default)

- **`cargo init`** - Initialize Cargo project in existing directory
  - `cargo init --lib` - Initialize as library
  - `cargo init --name <NAME>` - Set custom package name

### Dependency Management
- **`cargo add <PACKAGE>`** - Add dependency to Cargo.toml
  - `cargo add <PACKAGE> --dev` - Add as development dependency
  - `cargo add <PACKAGE> --build` - Add as build dependency
  - `cargo add <PACKAGE> --features <FEATURES>` - Add with specific features

- **`cargo remove <PACKAGE>`** - Remove dependency from Cargo.toml

- **`cargo search <QUERY>`** - Search for packages on crates.io

### Installation
- **`cargo install <PACKAGE>`** - Install binary crate
  - `cargo install --path .` - Install from current directory
  - `cargo install --git <URL>` - Install from git repository
  - `cargo install --force` - Force reinstall

- **`cargo uninstall <PACKAGE>`** - Remove installed binary

## 🚀 Publishing Commands

### Package Preparation
- **`cargo package`** - Create distributable tarball
  - `cargo package --list` - List files that would be packaged
  - `cargo package --allow-dirty` - Allow uncommitted changes

### Registry Authentication
- **`cargo login <TOKEN>`** - Save API token for registry
- **`cargo logout`** - Remove stored API token

### Publishing
- **`cargo publish`** - Upload package to registry
  - `cargo publish --dry-run` - Perform all checks without uploading
  - `cargo publish --allow-dirty` - Allow uncommitted changes

### Ownership Management
- **`cargo owner`** - Manage package owners
  - `cargo owner --add <LOGIN>` - Add owner
  - `cargo owner --remove <LOGIN>` - Remove owner
  - `cargo owner --list` - List current owners

- **`cargo yank --vers <VERSION>`** - Mark version as unusable
- **`cargo yank --vers <VERSION> --undo`** - Undo yank

## 🔧 Advanced Commands

### Workspace Management
- **`cargo workspaces`** - Commands for workspace management
  - `--workspace` flag works with most commands for workspace-wide operations

### Vendor and Offline
- **`cargo vendor`** - Vendor all dependencies locally
- **`cargo fetch`** - Fetch dependencies without building

### Information Commands
- **`cargo version`** - Show Cargo version
- **`cargo --list`** - List all available commands
- **`cargo help <COMMAND>`** - Show help for specific command

## 🎯 Common Command Combinations

### Development Workflow
```bash
# Full check before commit
cargo fmt --check && cargo clippy --all-targets --all-features -- -D warnings && cargo test

# Quick development cycle
cargo check && cargo test

# Release preparation
cargo fmt && cargo clippy --fix && cargo test && cargo doc --open
```

### CI/CD Pipeline
```bash
# Continuous Integration
cargo build --verbose
cargo test --verbose
cargo clippy -- -D warnings
cargo fmt --check
```

### Publishing Workflow
```bash
# Prepare for publish
cargo package --list
cargo publish --dry-run
cargo publish
```

## 🏃‍♂️ Performance Tips

- Use `cargo check` for faster feedback during development
- Use `cargo build --release` for optimized builds
- Use `cargo test --no-default-features` to test minimal feature sets
- Use `cargo build --offline` when dependencies are already downloaded
- Use `CARGO_INCREMENTAL=1` environment variable for incremental compilation

## 📝 Configuration

Cargo behavior can be customized through:
- `.cargo/config.toml` - Project-specific configuration
- `~/.cargo/config.toml` - Global user configuration
- Environment variables (e.g., `CARGO_TARGET_DIR`)

Use `cargo help config` for detailed configuration options.