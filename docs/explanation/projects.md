# Who uses manif

A curated list of work and projects using **manif**.

## They cite us

You may find on Google Scholar publications citing either:

- the paper [`"A micro Lie theory for state estimation in robotics", <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>](publication.md#micro-lie-theory) accompanying **manif** - at [Google Scholar][jsola18-scholar]
- the paper [`"Manif: A micro Lie theory library for state estimation in robotics applications" <PERSON><PERSON>, J<PERSON>](publication.md#manif) - at [Google Scholar][deray20-scholar]

## They use manif

- [`lie-group-controllers`][lie-group-controllers-repo], header-only C++ libraries containing controllers designed for Lie Groups.
- [`bipedal-locomotion-framework`][bipedal-locomotion-framework], the project is a suite of libraries for achieving bipedal locomotion on humanoid robots.
- [`kalmanif`][kalmanif], a small collection of Kalman Filters on Lie groups.
- [`cpp_filter`][cpp_filter], Kalman filters on Lie groups using C++ and **manif**.

Your project is not listed here? Let [us know](https://github.com/artivis/manif/issues) about it!

[//]: # (URLs)

[jsola18-scholar]: https://scholar.google.com/scholar?cites=16456301708818968338
[deray20-scholar]: https://scholar.google.com/scholar?cites=1235228860941456363

[lie-group-controllers-repo]: https://github.com/dic-iit/lie-group-controllers
[bipedal-locomotion-framework]: https://github.com/dic-iit/bipedal-locomotion-framework
[kalmanif]: https://github.com/artivis/kalmanif
[cpp_filter]: https://github.com/aalbaali/cpp_filter
