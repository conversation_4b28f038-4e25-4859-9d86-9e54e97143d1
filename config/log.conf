-- Main
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  true
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true
        SUBSECOND_PRECISION  =  6
        PERFORMANCE_TRACKING =  true
        MAX_LOG_FILE_SIZE    =  2097152 ## 2MB - Comment starts with two hashes (##)
        LOG_FLUSH_THRESHOLD  =  100 ## Flush after every 100 logs

-- System
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  true
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true

-- Depth
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  true
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true

-- Optimizer
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  false
        TO_FILE              =  false
        TO_STANDARD_OUTPUT   =  true

-- Algorithm
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  false
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  false

-- Feature
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  false
        TO_FILE              =  false
        TO_STANDARD_OUTPUT   =  true

-- Alignment
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  false
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true

-- Visualization
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  false
        TO_FILE              =  false
        TO_STANDARD_OUTPUT   =  true

-- Map
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  true
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true

-- Adjustment
    * GLOBAL:
        FORMAT               =  "[%datetime{%d.%M.%Y %H:%m:%s}] [%logger:%level:%thread] [%fbase:%line]: %msg"
        FILENAME             =  "../output/out.log"
        ENABLED              =  true
        TO_FILE              =  true
        TO_STANDARD_OUTPUT   =  true