# Gemini Code Assistant Context

This document provides context for the Gemini code assistant to understand the `apex-solver` project.

## Project Overview

`apex-solver` is a Rust-based library for efficient non-linear least squares optimization, with a focus on computer vision applications such as bundle adjustment, graph-based pose optimization, and SLAM. It is designed for high performance, flexibility, and scalability in visual-inertial systems and 3D reconstruction tasks.

The project is structured into several key modules:

*   **`manifold`**: Provides implementations of Lie groups (SE(2), SE(3), SO(2), SO(3)) for representing poses and rotations in 2D and 3D space.
*   **`io`**: Supports loading and processing of common graph file formats (G2O, TORO, TUM).
*   **`optimizer`**: Implements various optimization algorithms, including Gauss-Newton, Levenberg-Marquardt, and Dog Leg.
*   **`linalg`**: Provides linear algebra backends for the solvers, including sparse Cholesky and sparse QR decomposition.
*   **`core`**: Defines the core traits and structures for optimization problems.

## Building and Running

The project is built and managed using Cargo.

*   **Build the project:**
    ```bash
    cargo build
    ```
*   **Run tests:**
    ```bash
    cargo test
    ```
*   **Run examples:**
    ```bash
    # Analyze all graph files
    cargo run --example load_graph_file

    # Visualize a specific graph
    cargo run --example visualize_graph_file
    ```
*   **Run the solver demo:**
    ```bash
    cargo run --example solver_demo
    ```

## Development Conventions

*   **Formatting:** Code is formatted using `cargo fmt`.
*   **Linting:** Code is linted using `cargo clippy`.
*   **Testing:** The project has a comprehensive test suite. New features should be accompanied by tests.
*   **Documentation:** Public APIs should be well-documented with examples.
*   **Error Handling:** The project uses the `thiserror` crate for error handling. Errors should be handled gracefully and provide informative messages.
*   **Dependencies:** Dependencies are managed in `Cargo.toml`. Use `cargo add` to add new dependencies.
