@PACKAGE_INIT@

include(CMakeFindDependencyMacro)
@Eigen3_DEPENDENCY@
@tl-optional_DEPENDENCY@

if(NOT TARGET @PROJECT_NAME@)
  include("${CMAKE_CURRENT_LIST_DIR}/@<EMAIL>")
endif()

# Explicitly set ${PROJECT_NAME}_INCLUDE_DIRS
get_target_property(@PROJECT_NAME@_interface_includes
@PROJECT_NAME_CAPS@::@PROJECT_NAME@ INTERFACE_INCLUDE_DIRECTORIES)

set(@PROJECT_NAME@_INCLUDE_DIRS ${@PROJECT_NAME@_interface_includes})

set(@PROJECT_NAME@_LIBRARY ${@PROJECT_NAME@_LIBRARY})
set(@PROJECT_NAME@_LIBRARIES ${@PROJECT_NAME@_LIBRARY})

check_required_components("@PROJECT_NAME@")
