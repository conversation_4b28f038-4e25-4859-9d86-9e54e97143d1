name: documentation

on:
  push:
    branches: [ devel ]
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Update sources
        run: sudo apt-get update

      - name: Install doc dependencies
        run: sudo apt-get install -y doxygen

      # Those are necessary if pip doesn't find the wheels
      # and decides to rebuild from source
      - name: Install doc extra dependencies
        run: sudo apt-get install -y libjpeg-dev libxml2-dev libxslt1-dev

      - name: Install manif dependencies
        run: sudo apt-get install -y libeigen3-dev

      - name: Build Sphinx venv
        working-directory: "docs"
        run: |
          set -ex
          make install || ( cat .sphinx/venv/pip_install.log && exit 1 )

      - name: Build documentation
        working-directory: docs
        run: |
          make html || ( echo "Whole bunch of things to fix." )
          if ! [ -d _build ]; then exit 1; fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: doc
          path: |
            docs/_build
            !docs/_build/doxyxml

  test:
    needs: [build]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Update sources
        run: sudo apt-get update

      - name: Install doc dependencies
        run: sudo apt-get install -y doxygen

      # Those are necessary if pip doesn't find the wheels
      # and decides to rebuild from source
      - name: Install doc extra dependencies
        run: sudo apt-get install -y libjpeg-dev libxml2-dev libxslt1-dev

      - name: Install manif dependencies
        run: sudo apt-get install -y libeigen3-dev

      - name: linkcheck
        working-directory: docs
        run: make linkcheck

      - uses: DavidAnson/markdownlint-cli2-action@v16
        with:
          config: "docs/.sphinx/.markdownlint.json"

  # documentation-checks:
  #   uses: canonical/documentation-workflows/.github/workflows/documentation-checks.yaml@main
  #   with:
  #     working-directory: "docs"
  #     fetch-depth: 0
  #     install-target: "gh-ci-install"

  deploy:
    needs: [test]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    # todo: deploy if new tag/release
    if: |
      github.repository == 'artivis/manif' &&
      github.event_name == 'push' &&
      github.ref == 'refs/heads/devel'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: gh-pages

      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: doc
          path: docs_build

      - name: Deploy
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: gh-pages
          folder: docs_build
