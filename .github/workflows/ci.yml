name: build-and-test
on:
  push:
    branches:
      - devel
      - master
  pull_request:
    branches:
      - devel
  workflow_dispatch:

env:
  CTEST_OUTPUT_ON_FAILURE: 1

jobs:

  build-ubuntu:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-20.04, ubuntu-22.04, ubuntu-24.04]
        build_type: [Release, Debug]
        compiler: [
          {
            "cc": "gcc",
            "cxx": "g++"
          }, {
            "cc": "clang",
            "cxx": "clang++"
          }
        ]
    env:
      CC: ${{ matrix.compiler.cc }}
      CXX: ${{ matrix.compiler.cxx }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libeigen3-dev
          mkdir ${{ runner.workspace }}/build
      - name: Display config
        run: |
          echo "OS:" && $RUNNER_OS && echo ""
          echo "Compiler:" && $CC && $CXX && echo ""
          echo "Eigen:" && apt-cache policy libeigen3-dev | grep Installed
      - name: Configure
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DBUILD_EXAMPLES=ON -DBUILD_TESTING=ON -DCMAKE_BUILD_TYPE=${{ matrix.build_type }}
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test

  build-mac:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-13, macos-14, macos-15]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          brew install eigen
          mkdir ${{ runner.workspace }}/build
      - name: Display config
        run: |
          echo "OS:" && $RUNNER_OS && echo ""
          echo "Compiler:" && clang -v && echo ""
          echo "Eigen:" && brew info eigen
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DBUILD_EXAMPLES=ON -DBUILD_TESTING=ON -DCMAKE_BUILD_TYPE=Release
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test

  build-win:
    runs-on: ${{ matrix.combinations.os }}
    strategy:
      matrix:
        combinations: [
          {
            "os": "windows-2019",
            "cmake_generator": "Visual Studio 16 2019"
          }, {
            "os": "windows-2022",
            "cmake_generator": "Visual Studio 17 2022"
          }
        ]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          vcpkg install eigen3:x64-windows
          cd ${{ runner.workspace }}
          mkdir build
      - name: Display config
        shell: bash
        run: |
          echo "OS:" && $RUNNER_OS && echo ""
          echo "Eigen:" && vcpkg list
      - name: Configure CMake
        shell: bash
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -G"${{ matrix.combinations.cmake_generator }}" -A x64 -DCMAKE_TOOLCHAIN_FILE=${VCPKG_INSTALLATION_ROOT}/scripts/buildsystems/vcpkg.cmake -DBUILD_EXAMPLES=ON -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: cmake --build . --config Release
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: ctest . -C Release

  cppcheck:
    needs: [build-ubuntu]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libunwind-dev
          sudo apt install -y libceres-dev cppcheck
          mkdir ${{ runner.workspace }}/build
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DENABLE_CPPCHECK=ON -DBUILD_EXAMPLES=ON -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make run-cppcheck

  valgrind:
    needs: [build-ubuntu]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libunwind-dev
          sudo apt install -y libceres-dev valgrind
          mkdir ${{ runner.workspace }}/build
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DENABLE_VALGRIND=ON -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: ctest -T memcheck

  codecov:
    needs: [build-ubuntu]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libunwind-dev
          sudo apt install -y libceres-dev
          mkdir ${{ runner.workspace }}/build
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DENABLE_COVERAGE=ON -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test
      - name: Upload coverage
        working-directory: ${{ runner.workspace }}/build
        run: bash <(curl -s https://codecov.io/bash) -R $GITHUB_WORKSPACE

  ceres:
    needs: [build-ubuntu]
    # runs-on: ubuntu-latest
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      # Test for both Ceres pre/post 2.2
      matrix:
        os: [ubuntu-22.04, ubuntu-24.04]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libunwind-dev
          sudo apt install -y libceres-dev
          mkdir ${{ runner.workspace }}/build
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test

  autodiff:
    needs: [build-ubuntu]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup
        run: |
          sudo apt update
          sudo apt install -y libeigen3-dev
          mkdir ${{ runner.workspace }}/build
      # Install autodiff
      - name: Checkout autodiff
        uses: actions/checkout@v4
        with:
          repository: autodiff/autodiff
          ref: main
          path: 'autodiff'
      - name: Setup autodiff
        run: mkdir ${{ runner.workspace }}/build_autodiff
      - name: Configure CMake autodiff
        working-directory: ${{ runner.workspace }}/build_autodiff
        run: cmake $GITHUB_WORKSPACE/autodiff -DAUTODIFF_BUILD_TESTS=OFF -DAUTODIFF_BUILD_PYTHON=OFF -DAUTODIFF_BUILD_EXAMPLES=OFF -DAUTODIFF_BUILD_DOCS=OFF
      - name: Install autodiff
        working-directory: ${{ runner.workspace }}/build_autodiff
        run: sudo cmake --build . --target install
      # Build/test manif autodiff
      - name: Configure CMake
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DBUILD_TESTING=ON
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test

  pybind11-pip:
    needs: [build-ubuntu, build-mac]
    strategy:
      fail-fast: false
      matrix:
        platform: [macos-latest, ubuntu-latest] #windows-latest,
        python-version: ['3.8', '3.9', '3.10', '3.12']
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - run: git fetch --prune --unshallow
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Setup apt
        if: runner.os == 'Linux'
        run: |
          sudo apt update
          sudo apt install -y libeigen3-dev
      - name: Setup brew
        if: runner.os == 'macOS'
        run: brew install eigen
      - name: Setup
        run: |
          python -m pip install --upgrade pip
          python -m pip install build
      - name: Build
        run: python -m pip install -v .[testing]
      - name: Test
        run: python -m pytest

  pybind11-cmake:
    needs: [build-ubuntu, build-mac]
    strategy:
      matrix:
        platform: [ubuntu-20.04, ubuntu-22.04, ubuntu-24.04]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - run: git fetch --prune --unshallow
      # - name: Set up Python ${{ matrix.python-version }}
      #   uses: actions/setup-python@v2
      #   with:
      #     python-version: ${{ matrix.python-version }}
      - name: Setup apt
        run: |
          sudo apt update
          sudo apt install -y libeigen3-dev pybind11-dev python3-pytest python3-numpy
          mkdir ${{ runner.workspace }}/build
      - name: Configure
        working-directory: ${{ runner.workspace }}/build
        run: cmake $GITHUB_WORKSPACE -DBUILD_EXAMPLES=OFF -DBUILD_TESTING=ON -DBUILD_PYTHON_BINDINGS=ON -DCMAKE_BUILD_TYPE=Release
      - name: Build
        working-directory: ${{ runner.workspace }}/build
        run: make -j2
      - name: Test
        working-directory: ${{ runner.workspace }}/build
        run: make test
      - name: Install
        working-directory: ${{ runner.workspace }}/build
        run: sudo make install
      - name: Test Import
        run: python3 -c 'import manifpy'
