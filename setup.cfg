[metadata]
name = manifpy
description = A small library for Lie theory.
long_description = file: README.md; charset=UTF-8
long_description_content_type = text/markdown
author = <PERSON><PERSON><PERSON>
author_email = <EMAIL>
license = MIT
platforms = any
url = https://github.com/artivis/manif
project_urls =
    Source = https://github.com/artivis/manif
    Tracker = https://github.com/artivis/manif/issues
keywords = geometry lie-theory state-estimation slam robotics computer-vision
classifiers =
    Development Status :: 5 - Production/Stable
    Operating System :: OS Independent
    Operating System :: POSIX :: Linux
    Operating System :: MacOS
    Operating System :: Microsoft :: Windows
    Framework :: Robot Framework
    Intended Audience :: Science/Research
    Intended Audience :: Developers
    Intended Audience :: Education
    Programming Language :: C++
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    License :: OSI Approved :: MIT License

[options]
zip_safe = False
python_requires = >=3.6

[options.extras_require]
testing =
    pytest
    numpy
all =
    %(testing)s

[tool:pytest]
testpaths = test/python
