use std::ops::Mul;

use faer::{
    Mat,
    linalg::solvers::Solve,
    sparse::{SparseColMat, linalg::solvers},
};

use crate::linalg::SparseLinearSolver;

#[derive(Debug, <PERSON><PERSON>)]
pub struct SparseQRSolver {
    // Symbolic factorization can be cached if the sparsity pattern of the matrix is constant.
    // For augmented systems where lambda changes, this might not be safe to reuse.
    factorizer: Option<solvers::Qr<usize, f64>>,

    /// The Hessian matrix, computed as `(J^T * W * J)`.
    ///
    /// This is `None` if the Hessian could not be computed.
    hessian: Option<SparseColMat<usize, f64>>,

    /// The gradient vector, computed as `J^T * W * r`.
    ///
    /// This is `None` if the gradient could not be computed.
    gradient: Option<Mat<f64>>,

    /// The parameter covariance matrix, computed as `(J^T * W * J)^-1`.
    ///
    /// This is `None` if the Hessian is singular or ill-conditioned.
    covariance_matrix: Option<Mat<f64>>,
    /// Asymptotic standard errors of the parameters.
    ///
    /// This is `None` if the covariance matrix could not be computed.
    /// Each error is the square root of the corresponding diagonal element
    /// of the covariance matrix.
    standard_errors: Option<Mat<f64>>,
}

impl SparseQRSolver {
    pub fn new() -> Self {
        SparseQRSolver {
            factorizer: None,
            hessian: None,
            gradient: None,
            covariance_matrix: None,
            standard_errors: None,
        }
    }

    pub fn hessian(&self) -> Option<&SparseColMat<usize, f64>> {
        self.hessian.as_ref()
    }

    pub fn gradient(&self) -> Option<&Mat<f64>> {
        self.gradient.as_ref()
    }

    pub fn compute_covariance_matrix(&mut self) -> Option<&Mat<f64>> {
        // Only compute if we have a factorizer and hessian, but no covariance matrix yet
        if self.factorizer.is_some()
            && self.hessian.is_some()
            && self.covariance_matrix.is_none()
            && let (Some(factorizer), Some(hessian)) = (&self.factorizer, &self.hessian)
        {
            let n = hessian.ncols();
            // Create identity matrix
            let identity = Mat::identity(n, n);

            // Solve H * X = I to get X = H^(-1) = covariance matrix
            let cov_matrix = factorizer.solve(&identity);
            self.covariance_matrix = Some(cov_matrix);
        }
        self.covariance_matrix.as_ref()
    }

    pub fn compute_standard_errors(&mut self) -> Option<&Mat<f64>> {
        // // Ensure covariance matrix is computed first
        if self.covariance_matrix.is_none() {
            self.compute_covariance_matrix();
        }

        let n = self.hessian.as_ref().unwrap().ncols();
        // Compute standard errors as sqrt of diagonal elements
        if let Some(cov) = &self.covariance_matrix {
            let mut std_errors = Mat::zeros(n, 1);
            for i in 0..n {
                let diag_val = cov[(i, i)];
                if diag_val >= 0.0 {
                    std_errors[(i, 0)] = diag_val.sqrt();
                } else {
                    // Negative diagonal indicates numerical issues
                    return None;
                }
            }
            self.standard_errors = Some(std_errors);
        }
        self.standard_errors.as_ref()
    }

    /// Reset covariance computation state (useful for iterative optimization)
    pub fn reset_covariance(&mut self) {
        self.covariance_matrix = None;
        self.standard_errors = None;
    }
}

impl Default for SparseQRSolver {
    fn default() -> Self {
        Self::new()
    }
}

impl SparseLinearSolver for SparseQRSolver {
    fn solve_normal_equation(
        &mut self,
        residuals: &Mat<f64>,
        jacobians: &SparseColMat<usize, f64>,
        weights: &Mat<f64>,
    ) -> Option<Mat<f64>> {
        let m = jacobians.nrows();

        // Create a sparse diagonal matrix from the weights vector.
        let mut w_triplets = Vec::with_capacity(m);
        for i in 0..m {
            w_triplets.push(faer::sparse::Triplet::new(i, i, weights[(i, 0)]));
        }
        let weights_diag = SparseColMat::try_new_from_triplets(m, m, &w_triplets).unwrap();

        // Form the normal equations explicitly: H = J^T * W * J
        let hessian = jacobians
            .as_ref()
            .transpose()
            .to_col_major()
            .unwrap()
            .mul(weights_diag.as_ref().mul(jacobians.as_ref()));

        // g = J^T * W * -r
        let gradient = jacobians
            .as_ref()
            .transpose()
            .mul(weights_diag.as_ref().mul(-residuals));

        let sym = solvers::SymbolicQr::try_new(hessian.symbolic()).unwrap();
        if let Ok(qr) = solvers::Qr::try_new_with_symbolic(sym.clone(), hessian.as_ref()) {
            let dx = qr.solve(&gradient);
            self.hessian = Some(hessian);
            self.gradient = Some(gradient);
            self.factorizer = Some(qr);
            Some(dx)
        } else {
            None
        }
    }

    fn solve_augmented_equation(
        &mut self,
        residuals: &Mat<f64>,
        jacobians: &SparseColMat<usize, f64>,
        weights: &Mat<f64>,
        lambda: f64,
    ) -> Option<Mat<f64>> {
        let m = jacobians.nrows();
        let n = jacobians.ncols();

        // Create a sparse diagonal matrix from weights
        let mut w_triplets = Vec::with_capacity(m);
        for i in 0..m {
            w_triplets.push(faer::sparse::Triplet::new(i, i, weights[(i, 0)]));
        }
        let weights_diag = SparseColMat::try_new_from_triplets(m, m, &w_triplets).unwrap();

        // H = J^T * W * J
        let hessian = jacobians
            .as_ref()
            .transpose()
            .to_col_major()
            .unwrap()
            .mul(weights_diag.as_ref().mul(jacobians.as_ref()));

        // g = J^T * W * -r
        let gradient = jacobians
            .as_ref()
            .transpose()
            .mul(weights_diag.as_ref().mul(-residuals));

        // H_aug = H + lambda * I
        let mut lambda_i_triplets = Vec::with_capacity(n);
        for i in 0..n {
            lambda_i_triplets.push(faer::sparse::Triplet::new(i, i, lambda));
        }
        let lambda_i = SparseColMat::try_new_from_triplets(n, n, &lambda_i_triplets).unwrap();

        let augmented_hessian = hessian.as_ref() + lambda_i;

        // Don't cache symbolic pattern for augmented system as sparsity may change
        let sym = solvers::SymbolicQr::try_new(augmented_hessian.symbolic()).unwrap();
        if let Ok(qr) = solvers::Qr::try_new_with_symbolic(sym, augmented_hessian.as_ref()) {
            let dx = qr.solve(&gradient);
            self.hessian = Some(hessian);
            self.gradient = Some(gradient);
            self.factorizer = Some(qr);
            Some(dx)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use faer::Mat;
    use faer::sparse::SparseColMat;

    const TOLERANCE: f64 = 1e-10;

    /// Helper function to create test data for QR solver
    fn create_test_data() -> (SparseColMat<usize, f64>, Mat<f64>, Mat<f64>) {
        // Create a 4x3 overdetermined system
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 1.0),
            faer::sparse::Triplet::new(0, 1, 0.0),
            faer::sparse::Triplet::new(0, 2, 1.0),
            faer::sparse::Triplet::new(1, 0, 0.0),
            faer::sparse::Triplet::new(1, 1, 1.0),
            faer::sparse::Triplet::new(1, 2, 1.0),
            faer::sparse::Triplet::new(2, 0, 1.0),
            faer::sparse::Triplet::new(2, 1, 1.0),
            faer::sparse::Triplet::new(2, 2, 0.0),
            faer::sparse::Triplet::new(3, 0, 1.0),
            faer::sparse::Triplet::new(3, 1, 0.0),
            faer::sparse::Triplet::new(3, 2, 0.0),
        ];
        let jacobian = SparseColMat::try_new_from_triplets(4, 3, &triplets).unwrap();

        let residuals = Mat::from_fn(4, 1, |i, _| (i + 1) as f64);
        let weights = Mat::from_fn(4, 1, |_, _| 1.0);

        (jacobian, residuals, weights)
    }

    /// Test basic QR solver creation
    #[test]
    fn test_qr_solver_creation() {
        let solver = SparseQRSolver::new();
        assert!(solver.factorizer.is_none());

        let default_solver = SparseQRSolver::default();
        assert!(default_solver.factorizer.is_none());
    }

    /// Test normal equation solving with QR decomposition
    #[test]
    fn test_qr_solve_normal_equation() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        let solution = result.unwrap();
        assert_eq!(solution.nrows(), 3); // Number of variables
        assert_eq!(solution.ncols(), 1);

        // Verify symbolic pattern was cached
        assert!(solver.factorizer.is_some());
    }

    /// Test QR symbolic pattern caching
    #[test]
    fn test_qr_factorizer_caching() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        // First solve
        let result1 = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result1.is_some());
        assert!(solver.factorizer.is_some());

        // Second solve should reuse pattern
        let result2 = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result2.is_some());

        // Results should be identical
        let sol1 = result1.unwrap();
        let sol2 = result2.unwrap();
        for i in 0..sol1.nrows() {
            assert!((sol1[(i, 0)] - sol2[(i, 0)]).abs() < TOLERANCE);
        }
    }

    /// Test augmented equation solving with QR
    #[test]
    fn test_qr_solve_augmented_equation() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();
        let lambda = 0.1;

        let result = solver.solve_augmented_equation(&residuals, &jacobian, &weights, lambda);
        assert!(result.is_some());

        let solution = result.unwrap();
        assert_eq!(solution.nrows(), 3); // Number of variables
        assert_eq!(solution.ncols(), 1);
    }

    /// Test augmented system with different lambda values
    #[test]
    fn test_qr_augmented_different_lambdas() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        let lambda1 = 0.01;
        let lambda2 = 1.0;

        let result1 = solver.solve_augmented_equation(&residuals, &jacobian, &weights, lambda1);
        let result2 = solver.solve_augmented_equation(&residuals, &jacobian, &weights, lambda2);

        assert!(result1.is_some());
        assert!(result2.is_some());

        // Solutions should be different due to different regularization
        let sol1 = result1.unwrap();
        let sol2 = result2.unwrap();
        let mut different = false;
        for i in 0..sol1.nrows() {
            if (sol1[(i, 0)] - sol2[(i, 0)]).abs() > TOLERANCE {
                different = true;
                break;
            }
        }
        assert!(
            different,
            "Solutions should differ with different lambda values"
        );
    }

    /// Test QR with rank-deficient matrix
    #[test]
    fn test_qr_rank_deficient_matrix() {
        let mut solver = SparseQRSolver::new();

        // Create a rank-deficient matrix (3x3 but rank 2)
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 1.0),
            faer::sparse::Triplet::new(0, 1, 2.0),
            faer::sparse::Triplet::new(0, 2, 3.0),
            faer::sparse::Triplet::new(1, 0, 2.0),
            faer::sparse::Triplet::new(1, 1, 4.0),
            faer::sparse::Triplet::new(1, 2, 6.0), // 2x first row
            faer::sparse::Triplet::new(2, 0, 0.0),
            faer::sparse::Triplet::new(2, 1, 0.0),
            faer::sparse::Triplet::new(2, 2, 1.0),
        ];
        let jacobian = SparseColMat::try_new_from_triplets(3, 3, &triplets).unwrap();
        let residuals = Mat::from_fn(3, 1, |i, _| i as f64);
        let weights = Mat::from_fn(3, 1, |_, _| 1.0);

        // QR should still provide a least squares solution
        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());
    }

    /// Test QR with different weight distributions
    #[test]
    fn test_qr_different_weights() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, _) = create_test_data();

        let uniform_weights = Mat::from_fn(4, 1, |_, _| 1.0);
        let varying_weights = Mat::from_fn(4, 1, |i, _| (i + 1) as f64 * 0.5);

        let result1 = solver.solve_normal_equation(&residuals, &jacobian, &uniform_weights);
        let result2 = solver.solve_normal_equation(&residuals, &jacobian, &varying_weights);

        assert!(result1.is_some());
        assert!(result2.is_some());

        // Solutions should be different due to different weights
        let sol1 = result1.unwrap();
        let sol2 = result2.unwrap();
        let mut different = false;
        for i in 0..sol1.nrows() {
            if (sol1[(i, 0)] - sol2[(i, 0)]).abs() > TOLERANCE {
                different = true;
                break;
            }
        }
        assert!(different, "Solutions should differ with different weights");
    }

    /// Test augmented system structure and dimensions
    #[test]
    fn test_qr_augmented_system_structure() {
        let mut solver = SparseQRSolver::new();

        // Simple 2x2 system
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 1.0),
            faer::sparse::Triplet::new(0, 1, 0.0),
            faer::sparse::Triplet::new(1, 0, 0.0),
            faer::sparse::Triplet::new(1, 1, 1.0),
        ];
        let jacobian = SparseColMat::try_new_from_triplets(2, 2, &triplets).unwrap();
        let residuals = Mat::from_fn(2, 1, |i, _| (i + 1) as f64);
        let weights = Mat::from_fn(2, 1, |_, _| 1.0);
        let lambda = 0.5;

        let result = solver.solve_augmented_equation(&residuals, &jacobian, &weights, lambda);
        assert!(result.is_some());

        let solution = result.unwrap();
        assert_eq!(solution.nrows(), 2); // Should return only the variable part
        assert_eq!(solution.ncols(), 1);
    }

    /// Test numerical accuracy with known solution
    #[test]
    fn test_qr_numerical_accuracy() {
        let mut solver = SparseQRSolver::new();

        // Create identity system: I * x = b
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 1.0),
            faer::sparse::Triplet::new(1, 1, 1.0),
            faer::sparse::Triplet::new(2, 2, 1.0),
        ];
        let jacobian = SparseColMat::try_new_from_triplets(3, 3, &triplets).unwrap();
        let residuals = Mat::from_fn(3, 1, |i, _| -((i + 1) as f64)); // [-1, -2, -3]
        let weights = Mat::from_fn(3, 1, |_, _| 1.0);

        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        let solution = result.unwrap();
        // Expected solution should be [1, 2, 3]
        for i in 0..3 {
            let expected = (i + 1) as f64;
            assert!(
                (solution[(i, 0)] - expected).abs() < TOLERANCE,
                "Expected {}, got {}",
                expected,
                solution[(i, 0)]
            );
        }
    }

    /// Test QR solver clone functionality
    #[test]
    fn test_qr_solver_clone() {
        let solver1 = SparseQRSolver::new();
        let solver2 = solver1.clone();

        assert!(solver1.factorizer.is_none());
        assert!(solver2.factorizer.is_none());
    }

    /// Test zero lambda in augmented system (should behave like normal equation)
    #[test]
    fn test_qr_zero_lambda_augmented() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        let normal_result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        let augmented_result =
            solver.solve_augmented_equation(&residuals, &jacobian, &weights, 0.0);

        assert!(normal_result.is_some());
        assert!(augmented_result.is_some());

        let normal_sol = normal_result.unwrap();
        let augmented_sol = augmented_result.unwrap();

        // Solutions should be very close (within numerical precision)
        for i in 0..normal_sol.nrows() {
            assert!(
                (normal_sol[(i, 0)] - augmented_sol[(i, 0)]).abs() < 1e-8,
                "Zero lambda augmented should match normal equation"
            );
        }
    }

    /// Test with very small weights (near-zero)
    #[test]
    fn test_qr_small_weights() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, _) = create_test_data();

        let small_weights = Mat::from_fn(4, 1, |_, _| 1e-12);

        let result = solver.solve_normal_equation(&residuals, &jacobian, &small_weights);
        // Should still work, though solution might be less meaningful
        assert!(result.is_some());
    }

    /// Test covariance matrix computation
    #[test]
    fn test_qr_covariance_computation() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        // First solve to set up factorizer and hessian
        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        // Now compute covariance matrix
        let cov_matrix = solver.compute_covariance_matrix();
        assert!(cov_matrix.is_some());

        let cov = cov_matrix.unwrap();
        assert_eq!(cov.nrows(), 3); // Should be n x n where n is number of variables
        assert_eq!(cov.ncols(), 3);

        // Covariance matrix should be symmetric
        for i in 0..3 {
            for j in 0..3 {
                assert!(
                    (cov[(i, j)] - cov[(j, i)]).abs() < TOLERANCE,
                    "Covariance matrix should be symmetric"
                );
            }
        }

        // Diagonal elements should be positive (variances)
        for i in 0..3 {
            assert!(
                cov[(i, i)] > 0.0,
                "Diagonal elements (variances) should be positive"
            );
        }
    }

    /// Test standard errors computation
    #[test]
    fn test_qr_standard_errors_computation() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        // First solve to set up factorizer and hessian
        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        // Compute covariance matrix first (this also computes standard errors)
        solver.compute_standard_errors();

        // Now check that both covariance matrix and standard errors are available
        assert!(solver.covariance_matrix.is_some());
        assert!(solver.standard_errors.is_some());

        let cov = solver.covariance_matrix.as_ref().unwrap();
        let errors = solver.standard_errors.as_ref().unwrap();

        assert_eq!(errors.nrows(), 3); // Should be n x 1 where n is number of variables
        assert_eq!(errors.ncols(), 1);

        // All standard errors should be positive
        for i in 0..3 {
            assert!(errors[(i, 0)] > 0.0, "Standard errors should be positive");
        }

        // Verify relationship: std_error = sqrt(covariance_diagonal)
        for i in 0..3 {
            let expected_std_error = cov[(i, i)].sqrt();
            assert!(
                (errors[(i, 0)] - expected_std_error).abs() < TOLERANCE,
                "Standard error should equal sqrt of covariance diagonal"
            );
        }
    }

    /// Test covariance computation with well-conditioned system
    #[test]
    fn test_qr_covariance_well_conditioned() {
        let mut solver = SparseQRSolver::new();

        // Create a well-conditioned 2x2 system
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 2.0),
            faer::sparse::Triplet::new(0, 1, 0.0),
            faer::sparse::Triplet::new(1, 0, 0.0),
            faer::sparse::Triplet::new(1, 1, 3.0),
        ];
        let jacobian = SparseColMat::try_new_from_triplets(2, 2, &triplets).unwrap();
        let residuals = Mat::from_fn(2, 1, |i, _| (i + 1) as f64);
        let weights = Mat::from_fn(2, 1, |_, _| 1.0);

        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        let cov_matrix = solver.compute_covariance_matrix();
        assert!(cov_matrix.is_some());

        let cov = cov_matrix.unwrap();
        // For this system, H = J^T * W * J = [[4, 0], [0, 9]]
        // So covariance = H^(-1) = [[1/4, 0], [0, 1/9]]
        assert!((cov[(0, 0)] - 0.25).abs() < TOLERANCE);
        assert!((cov[(1, 1)] - 1.0 / 9.0).abs() < TOLERANCE);
        assert!(cov[(0, 1)].abs() < TOLERANCE);
        assert!(cov[(1, 0)].abs() < TOLERANCE);
    }

    /// Test covariance computation caching
    #[test]
    fn test_qr_covariance_caching() {
        let mut solver = SparseQRSolver::new();
        let (jacobian, residuals, weights) = create_test_data();

        // First solve
        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        assert!(result.is_some());

        // First covariance computation
        solver.compute_covariance_matrix();
        assert!(solver.covariance_matrix.is_some());

        // Get pointer to first computation
        let cov1_ptr = solver.covariance_matrix.as_ref().unwrap().as_ptr();

        // Second covariance computation should return cached result
        solver.compute_covariance_matrix();
        assert!(solver.covariance_matrix.is_some());

        // Get pointer to second computation
        let cov2_ptr = solver.covariance_matrix.as_ref().unwrap().as_ptr();

        // Should be the same pointer (cached)
        assert_eq!(cov1_ptr, cov2_ptr, "Covariance matrix should be cached");
    }

    /// Test that covariance computation fails gracefully for singular systems
    #[test]
    fn test_qr_covariance_singular_system() {
        let mut solver = SparseQRSolver::new();

        // Create a singular system (rank deficient)
        let triplets = vec![
            faer::sparse::Triplet::new(0, 0, 1.0),
            faer::sparse::Triplet::new(0, 1, 2.0),
            faer::sparse::Triplet::new(1, 0, 2.0),
            faer::sparse::Triplet::new(1, 1, 4.0), // Second row is 2x first row
        ];
        let jacobian = SparseColMat::try_new_from_triplets(2, 2, &triplets).unwrap();
        let residuals = Mat::from_fn(2, 1, |i, _| i as f64);
        let weights = Mat::from_fn(2, 1, |_, _| 1.0);

        // QR can handle rank-deficient systems, but covariance may be problematic
        let result = solver.solve_normal_equation(&residuals, &jacobian, &weights);
        if result.is_some() {
            // If solve succeeded, covariance computation might still fail due to singularity
            let cov_matrix = solver.compute_covariance_matrix();
            // We don't assert failure here since QR might handle this case
            if let Some(cov) = cov_matrix {
                // If covariance is computed, check that it's reasonable
                assert!(cov.nrows() == 2);
                assert!(cov.ncols() == 2);
            }
        }
    }
}
